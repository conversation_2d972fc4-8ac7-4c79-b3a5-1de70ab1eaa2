import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, AuthUser, UserR<PERSON> } from '@/lib/auth/server-exports';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { z } from 'zod';
import { createDataAccess } from '@/lib/data-access';

/**
 * API endpoint for managing alert configurations
 * This endpoint is protected and should only be accessible to authenticated users with proper permissions
 */
export const GET = withAuth(
  [UserRole.Partner],
  async (_req: NextRequest, user: AuthUser, supabase: SupabaseClient<Database>, context: any) => {
  console.log('Security alert configs API called');
  const userId = user.id;

  try {
    console.log('Security alert configs API authenticated with user:', userId);

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const requestedUserId = searchParams.get('userId');

    console.log('Security alert configs API parameters:', { requestedUserId });

    // If no userId is provided, use the authenticated user's ID
    const targetUserId = requestedUserId || userId;

    // Check if the users table exists
    try {
      // Check if the user exists in tenants.users table
      const { data: userData, error: userError } = await supabase
        .schema('tenants')
        .from('users')
        .select('role, tenant_id')
        .eq('auth_user_id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);

        // If the user doesn't exist, return mock data for development
        console.log('User not found, returning mock data');
        return NextResponse.json({
          data: createMockAlertConfig(targetUserId),
          mock: true
        });
      }

      if (!userData) {
        console.log('User data is null, returning mock data');
        return NextResponse.json({
          data: createMockAlertConfig(targetUserId),
          mock: true
        });
      }

      console.log('User data found:', userData);

      // Check if the user is requesting their own config or if they have admin access
      if (targetUserId !== userId) {
        const isAdmin = userData.role === 'admin' || userData.role === 'superadmin' || userData.role === 'partner';

        if (!isAdmin) {
          return NextResponse.json(
            { error: 'Unauthorized access to another user\'s configuration' },
            { status: 403 }
          );
        }

        // For tenant admins, check if the requested user is in their tenant
        if (userData.role !== 'superadmin') {
          const { data: targetUserData, error: targetUserError } = await supabase
            .schema('tenants')
            .from('users')
            .select('tenant_id')
            .eq('auth_user_id', targetUserId)
            .single();

          if (targetUserError || !targetUserData || targetUserData.tenant_id !== userData.tenant_id) {
            return NextResponse.json(
              { error: 'Unauthorized access to user from another tenant' },
              { status: 403 }
            );
          }
        }
      }

      const tenantId = userData.tenant_id;

      // Check if the security.alert_configs table exists
      try {
        // Try to query the security.alert_configs table
        const { error: tableError } = await supabase
          .schema('security')
          .from('alert_configs')
          .select('id')
          .limit(1);

        if (tableError) {
          console.error('Error checking security.alert_configs table:', tableError);
          return NextResponse.json({
            data: createMockAlertConfig(targetUserId),
            mock: true
          });
        }

        console.log('Security.alert_configs table exists');
      } catch (tableCheckError) {
        console.error('Error checking security.alert_configs table:', tableCheckError);
        return NextResponse.json({
          data: createMockAlertConfig(targetUserId),
          mock: true
        });
      }

      // Get the user's alert configuration
      const { data, error } = await supabase
        .schema('security')
        .from('alert_configs')
        .select('*')
        .eq('user_id', targetUserId)
        .eq('tenant_id', tenantId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching alert configurations:', error);
        return NextResponse.json({
          data: createMockAlertConfig(targetUserId),
          mock: true
        });
      }

      // If no configuration exists, return default values
      if (!data) {
        return NextResponse.json({
          data: {
            id: null,
            user_id: targetUserId,
            tenant_id: tenantId,
            email: true,
            in_app: true,
            sms: false,
            min_severity: 'medium',
            created_at: null,
            updated_at: null
          }
        });
      }

      console.log('Returning alert configuration for user:', targetUserId);
      return NextResponse.json({ data });
    } catch (userCheckError) {
      console.error('Error checking users table:', userCheckError);
      return NextResponse.json({
        data: createMockAlertConfig(targetUserId),
        mock: true
      });
    }
  } catch (err) {
    console.error('Error in alert configurations API:', err);
    // Return mock data in case of error
    return NextResponse.json({
      data: createMockAlertConfig(req.nextUrl.searchParams.get('userId') || userId),
      mock: true
    });
  }
});

interface AlertConfig {
  id: string;
  user_id: string;
  tenant_id: string;
  email: boolean;
  in_app: boolean;
  sms: boolean;
  min_severity: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  updated_at: string;
}

function createMockAlertConfig(userId: string): AlertConfig {
  return {
    id: 'mock-config-1',
    user_id: userId,
    tenant_id: 'mock-tenant-1',
    email: true,
    in_app: true,
    sms: false,
    min_severity: 'medium',
    created_at: new Date(Date.now() - 86400000).toISOString(),
    updated_at: new Date().toISOString()
  };
}

export const POST = withAuth(
  [UserRole.Partner],
  async (_req: NextRequest, user: AuthUser, supabase: SupabaseClient<Database>, context: any) => {
  console.log('Handling POST request for security alerts config...');
  const userId = user.id;

  try {
    console.log('POST request authenticated with user:', userId);

    // Get the request body
    const body = await req.json();
    const { userId: targetUserId, ...configData } = body;

    console.log('Security alert configs POST API parameters:', { targetUserId, configData });

    // If no userId is provided, use the authenticated user's ID
    const userIdToUpdate = targetUserId || userId;

    // Check if the users table exists
    try {
      // Check if the user exists in tenants.users table
      const { data: userData, error: userError } = await supabase
        .schema('tenants')
        .from('users')
        .select('role, tenant_id')
        .eq('auth_user_id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user data:', userError);

        // If the user doesn't exist, return mock data for development
        console.log('User not found, returning mock success response');
        return NextResponse.json({
          data: createMockAlertConfig(userIdToUpdate),
          mock: true
        });
      }

      if (!userData) {
        console.log('User data is null, returning mock success response');
        return NextResponse.json({
          data: createMockAlertConfig(userIdToUpdate),
          mock: true
        });
      }

      console.log('User data found:', userData);

      // Check if the user is updating their own config or if they have admin access
      if (userIdToUpdate !== userId) {
        const isAdmin = userData.role === 'admin' || userData.role === 'superadmin' || userData.role === 'partner';

        if (!isAdmin) {
          return NextResponse.json(
            { error: 'Unauthorized access to update another user\'s configuration' },
            { status: 403 }
          );
        }

        // For tenant admins, check if the target user is in their tenant
        if (userData.role !== 'superadmin') {
          const { data: targetUserData, error: targetUserError } = await supabase
            .schema('tenants')
            .from('users')
            .select('tenant_id')
            .eq('auth_user_id', userIdToUpdate)
            .single();

          if (targetUserError || !targetUserData || targetUserData.tenant_id !== userData.tenant_id) {
            return NextResponse.json(
              { error: 'Unauthorized access to update user from another tenant' },
              { status: 403 }
            );
          }
        }
      }

      const tenantId = userData.tenant_id;

      // Check if the security.alert_configs table exists
      try {
        // Try to query the security.alert_configs table
        const { error: tableError } = await supabase
          .schema('security')
          .from('alert_configs')
          .select('id')
          .limit(1);

        if (tableError) {
          console.error('Error checking security.alert_configs table:', tableError);
          return NextResponse.json({
            data: createMockAlertConfig(userIdToUpdate),
            mock: true
          });
        }

        console.log('Security.alert_configs table exists');
      } catch (tableCheckError) {
        console.error('Error checking security.alert_configs table:', tableCheckError);
        return NextResponse.json({
          data: createMockAlertConfig(userIdToUpdate),
          mock: true
        });
      }

      // Check if configuration exists for the user
      const { data: existingConfig, error: fetchError } = await supabase
        .schema('security')
        .from('alert_configs')
        .select('id')
        .eq('user_id', userIdToUpdate)
        .eq('tenant_id', tenantId)
        .maybeSingle();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error checking alert configuration:', fetchError);
        return NextResponse.json({
          data: createMockAlertConfig(userIdToUpdate),
          mock: true
        });
      }

      let result;

      if (existingConfig) {
        // Update existing configuration
        result = await supabase
          .schema('security')
          .from('alert_configs')
          .update({
            ...configData,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userIdToUpdate)
          .eq('tenant_id', tenantId)
          .select()
          .single();
      } else {
        // Create new configuration
        result = await supabase
          .schema('security')
          .from('alert_configs')
          .insert({
            user_id: userIdToUpdate,
            tenant_id: tenantId,
            ...configData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();
      }

      if (result.error) {
        console.error('Error updating alert configuration:', result.error);
        return NextResponse.json({
          data: createMockAlertConfig(userIdToUpdate),
          mock: true
        });
      }

      // Check if the security.events table exists
      try {
        // Try to query the security.events table
        const { error: eventsTableError } = await supabase
          .schema('security')
          .from('events')
          .select('id')
          .limit(1);

        if (!eventsTableError) {
          // Log the configuration update as a security event
          await supabase
            .schema('security')
            .from('events')
            .insert({
              event_type: 'security.alert_config_updated',
              event_category: 'security',
              user_id: userId,
              details: {
                target_user_id: userIdToUpdate,
                changes: configData
              },
              created_at: new Date().toISOString()
            });
        }
      } catch (eventsTableError) {
        console.error('Error checking or logging to security.events table:', eventsTableError);
      }

      console.log('Alert configuration updated successfully');
      return NextResponse.json({ data: result.data });
    } catch (userCheckError) {
      console.error('Error checking users table:', userCheckError);
      return NextResponse.json({
        data: createMockAlertConfig(userIdToUpdate),
        mock: true
      });
    }
  } catch (err) {
    console.error('Error in alert configurations API:', err);
    // Return mock data in case of error
    return NextResponse.json({
      data: createMockAlertConfig(userId),
      mock: true
    });
  }
});
