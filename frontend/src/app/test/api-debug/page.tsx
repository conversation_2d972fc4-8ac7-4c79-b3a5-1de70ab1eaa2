'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useUser } from '@/contexts/UserContext';
import { CheckCircle2, XCircle, AlertTriangle } from 'lucide-react';

export default function ApiDebugPage() {
  const { user, role: userRole } = useUser();
  const [results, setResults] = useState<Record<string, unknown>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});

  const endpoints = [
    { name: 'Auth Test', url: '/api/test-auth' },
    { name: 'Templates Test', url: '/api/templates/test' },
    { name: 'Templates API', url: '/api/templates' }
  ];

  const testEndpoint = async (endpoint: string, name: string) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({ ...prev, [name]: null }));

    try {
      console.log(`Testing endpoint: ${endpoint}`);
      const response = await fetch(endpoint);
      console.log(`Response status: ${response.status}`);

      let data;
      try {
        data = await response.json();
        console.log('Response data:', data);
      } catch (jsonError) {
        console.error('Failed to parse response as JSON:', jsonError);
        throw new Error('Invalid response format');
      }

      setResults(prev => ({
        ...prev,
        [name]: {
          status: response.status,
          data: data,
          timestamp: new Date().toISOString()
        }
      }));

      if (!response.ok) {
        setErrors(prev => ({
          ...prev,
          [name]: `Error ${response.status}: ${data.error || 'Unknown error'}`
        }));
      }
    } catch (err) {
      console.error(`Error testing ${endpoint}:`, err);
      setErrors(prev => ({
        ...prev,
        [name]: err instanceof Error ? err.message : 'Unknown error'
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">API Debugging Tool</h1>

      <div className="mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Current User</CardTitle>
            <CardDescription>Authentication status and role information</CardDescription>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-2">
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Role:</strong> {userRole}</p>
                <p><strong>ID:</strong> {user.id}</p>
              </div>
            ) : (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Not authenticated</AlertTitle>
                <AlertDescription>
                  You are not currently logged in. Please log in to test the API endpoints.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6">
        {endpoints.map((endpoint) => (
          <Card key={endpoint.name}>
            <CardHeader>
              <CardTitle>{endpoint.name}</CardTitle>
              <CardDescription>{endpoint.url}</CardDescription>
            </CardHeader>
            <CardContent>
              {errors[endpoint.name] && (
                <Alert variant="destructive" className="mb-4">
                  <XCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{errors[endpoint.name]}</AlertDescription>
                </Alert>
              )}

              {results[endpoint.name] && !errors[endpoint.name] && (
                <Alert variant="success" className="mb-4 bg-green-50 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <AlertTitle className="text-green-700">Success</AlertTitle>
                  <AlertDescription className="text-green-600">
                    Status: {results[endpoint.name].status}
                  </AlertDescription>
                </Alert>
              )}

              {results[endpoint.name] && (
                <div className="mt-4">
                  <Tabs defaultValue="formatted">
                    <TabsList>
                      <TabsTrigger value="formatted">Formatted</TabsTrigger>
                      <TabsTrigger value="raw">Raw JSON</TabsTrigger>
                    </TabsList>
                    <TabsContent value="formatted" className="mt-2">
                      <div className="bg-slate-50 p-4 rounded-md">
                        <pre className="text-sm whitespace-pre-wrap">
                          {results[endpoint.name]?.data ? (
                            <ul className="space-y-2">
                              {Object.entries(results[endpoint.name].data).map(([key, value]) => (
                                <li key={key}>
                                  <strong>{key}:</strong> {' '}
                                  {typeof value === 'object'
                                    ? JSON.stringify(value, null, 2)
                                    : String(value)
                                  }
                                </li>
                              ))}
                            </ul>
                          ) : 'No data'}
                        </pre>
                      </div>
                    </TabsContent>
                    <TabsContent value="raw" className="mt-2">
                      <div className="bg-slate-50 p-4 rounded-md overflow-auto max-h-96">
                        <pre className="text-sm">
                          {JSON.stringify(results[endpoint.name]?.data, null, 2) || 'No data'}
                        </pre>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => testEndpoint(endpoint.url, endpoint.name)}
                disabled={loading[endpoint.name]}
              >
                {loading[endpoint.name] ? 'Testing...' : 'Test Endpoint'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
