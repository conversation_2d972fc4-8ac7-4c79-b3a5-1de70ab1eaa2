import type { Database, Json } from '../../lib/supabase/database.types';
import type { Case, CaseInput, CaseStatus } from '../../types/domain/tenants/Case';

// Get the actual database types from Supabase generated types
type CaseRow = Database['tenants']['Tables']['cases']['Row'];
type CaseInsert = Database['tenants']['Tables']['cases']['Insert'];
type CaseUpdate = Database['tenants']['Tables']['cases']['Update'];

/**
 * Maps a cases row from the database to our frontend Case domain model
 * This isolates all field name transformations in one place
 */
export function mapCaseFromDb(row: CaseRow): Case {
  // Extract metadata from the JSON field if available
  const metadata = row.metadata as Record<string, unknown> | null;

  // Transform the primary attorney data if present
  let primaryAttorney = null;
  const joinedAttorney = (row as any).primary_attorney;

  if (joinedAttorney && typeof joinedAttorney === 'object') {
    primaryAttorney = {
      id: joinedAttorney.id,
      fullName: [joinedAttorney.first_name, joinedAttorney.last_name].filter(Boolean).join(' '),
      email: joinedAttorney.email || null
    };
  }

  // Transform joined clients if present
  const clients = [];
  const joinedClients = (row as any).clients;

  if (joinedClients && Array.isArray(joinedClients)) {
    for (const client of joinedClients) {
      clients.push({
        id: client.id,
        fullName: [client.first_name, client.middle_name, client.last_name].filter(Boolean).join(' '),
        email: client.email || null
      });
    }
  }

  // Transform counts if present
  const documentCount = (row as any).document_count || 0;
  const deadlineCount = (row as any).deadline_count || 0;
  const noteCount = (row as any).note_count || 0;

  return {
    id: row.id,
    tenantId: row.tenant_id,
    title: row.title,
    description: row.description,
    status: row.status as CaseStatus,
    practiceArea: row.practice_area,
    // Map caseNumber from docket_number in DB
    caseNumber: metadata?.case_number || null,
    // Map courtName from metadata
    courtName: metadata?.court_name || null,
    jurisdiction: metadata?.jurisdiction || null,
    // Map filingDate from opened_date in DB
    filingDate: row.opened_date,
    // Map trialDate from metadata
    trialDate: metadata?.trial_date || null,
    // Map primaryAttorneyId from metadata
    primaryAttorneyId: metadata?.primary_attorney_id || null,
    // Map priorityLevel from intake_priority in DB
    priorityLevel: row.intake_priority,
    // Map statueOfLimitations from metadata
    statueOfLimitations: metadata?.statue_of_limitations || null,
    createdBy: row.created_by || '',
    createdAt: row.created_at || new Date().toISOString(),
    updatedBy: metadata?.updated_by || null,
    updatedAt: row.updated_at || null,

    // Joined data
    primaryAttorney,
    clients: clients.length > 0 ? clients : undefined,

    // Counts
    documentCount,
    deadlineCount,
    noteCount
  };
}

/**
 * Maps multiple case records from the database
 */
export function mapCasesFromDb(rows: CaseRow[]): Case[] {
  return rows.map(mapCaseFromDb);
}

/**
 * Maps a domain case model to a database insert object
 * This prepares the data for insertion into the database
 */
export function mapCaseToDb(caseData: CaseInput, userId: string, tenantId: string, clientId: string): CaseInsert {
  // Store extra metadata in JSON field
  const metadata: Json = {
    case_number: caseData.caseNumber,
    court_name: caseData.courtName,
    jurisdiction: caseData.jurisdiction,
    trial_date: caseData.trialDate,
    primary_attorney_id: caseData.primaryAttorneyId,
    statue_of_limitations: caseData.statueOfLimitations,
    updated_by: null
  };

  return {
    tenant_id: tenantId,
    title: caseData.title,
    description: caseData.description,
    status: caseData.status || 'open',
    practice_area: caseData.practiceArea,
    opened_date: caseData.filingDate,
    intake_priority: caseData.priorityLevel,
    client_id: clientId, // Required by schema
    created_by: userId,
    metadata
  };
}

/**
 * Maps case-client associations for batch insertion
 */
export function mapCaseClientsToDb(caseId: string, clientIds: string[], tenantId: string): {
  case_id: string;
  client_id: string;
  tenant_id: string;
}[] {
  return clientIds.map(clientId => ({
    case_id: caseId,
    client_id: clientId,
    tenant_id: tenantId
  }));
}

/**
 * Maps a domain case update to a database update object
 * Only includes fields that can be updated by users
 */
export function mapCaseUpdateToDb(
  caseData: Partial<CaseInput>,
  userId: string,
  originalCase: CaseRow
): Partial<CaseUpdate> {
  const updates: Partial<CaseUpdate> = {
    updated_at: new Date().toISOString()
  };

  // Direct field updates
  if (caseData.title !== undefined) {
    updates.title = caseData.title;
  }

  if (caseData.description !== undefined) {
    updates.description = caseData.description;
  }

  if (caseData.status !== undefined) {
    updates.status = caseData.status || 'open';
  }

  if (caseData.practiceArea !== undefined) {
    updates.practice_area = caseData.practiceArea;
  }

  if (caseData.filingDate !== undefined) {
    updates.opened_date = caseData.filingDate;
  }

  if (caseData.priorityLevel !== undefined) {
    updates.intake_priority = caseData.priorityLevel;
  }

  // Handle metadata updates
  const originalMetadata = originalCase.metadata as Record<string, unknown> || {};
  let metadataUpdated = false;
  const newMetadata = { ...originalMetadata };

  if (caseData.caseNumber !== undefined) {
    newMetadata.case_number = caseData.caseNumber;
    metadataUpdated = true;
  }

  if (caseData.courtName !== undefined) {
    newMetadata.court_name = caseData.courtName;
    metadataUpdated = true;
  }

  if (caseData.jurisdiction !== undefined) {
    newMetadata.jurisdiction = caseData.jurisdiction;
    metadataUpdated = true;
  }

  if (caseData.trialDate !== undefined) {
    newMetadata.trial_date = caseData.trialDate;
    metadataUpdated = true;
  }

  if (caseData.primaryAttorneyId !== undefined) {
    newMetadata.primary_attorney_id = caseData.primaryAttorneyId;
    metadataUpdated = true;
  }

  if (caseData.statueOfLimitations !== undefined) {
    newMetadata.statue_of_limitations = caseData.statueOfLimitations;
    metadataUpdated = true;
  }

  // Update the metadata and updater info if any metadata field was updated
  if (metadataUpdated) {
    newMetadata.updated_by = userId;
    updates.metadata = newMetadata as Json;
  }

  return updates;
}
