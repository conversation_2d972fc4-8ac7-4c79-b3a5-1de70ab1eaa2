/**
 * Type definitions for Copilotkit components
 */

import { ReactNode } from 'react';

declare module '@copilotkit/react-core' {
  export interface CopilotChatProps {
    // Base props
    className?: string;
    children?: ReactNode;

    // Extended props that might not be in the official types
    displayName?: string;
    placeholder?: string;
    suggestions?: string[];
    context?: Record<string, unknown>;
    initialMessage?: string;
    onError?: (error: Error) => void;
    agent?: unknown;
    title?: string;
    showAvatars?: boolean;
  }

  export interface CopilotRuntimeConstructorParams {
    // Base props
    apiKey?: string;
    baseUrl?: string;

    // Extended props that might not be in the official types
    openAIApiKey?: string;
    anthropicApiKey?: string;
    azureOpenAIApiKey?: string;
    azureOpenAIEndpoint?: string;
    azureOpenAIDeploymentName?: string;
    azureOpenAIApiVersion?: string;
  }

  export interface CopilotDeprecatedRuntimeConstructorParams {
    // Base props
    apiKey?: string;
    baseUrl?: string;

    // Extended props that might not be in the official types
    openAIApiKey?: string;
    anthropicApiKey?: string;
    azureOpenAIApiKey?: string;
    azureOpenAIEndpoint?: string;
    azureOpenAIDeploymentName?: string;
    azureOpenAIApiVersion?: string;
  }

  export interface CopilotBackend {
    // Methods
    text: (_req: Request) => Promise<Response>;
    chat: (_req: Request) => Promise<Response>;
    stream: (_req: Request) => Promise<Response>;
    streamText: (_req: Request) => Promise<Response>;
    streamChat: (_req: Request) => Promise<Response>;
  }
}

declare module '@copilotkit/react-ui' {
  import { CopilotChatProps } from '@copilotkit/react-core';

  export const CopilotChat: React.FC<CopilotChatProps>;
}

declare module '@copilotkit/react-textarea' {
  export interface CopilotTextareaProps {
    className?: string;
    placeholder?: string;
    context?: Record<string, unknown>;
    value?: string;
    onChange?: (value: string) => void;
    onError?: (error: Error) => void;
  }

  export const CopilotTextarea: React.FC<CopilotTextareaProps>;
}

declare module '@copilotkit/backend' {
  import { CopilotBackend, CopilotRuntimeConstructorParams } from '@copilotkit/react-core';

  export function createBackend(
    params?: CopilotRuntimeConstructorParams
  ): CopilotBackend;
}

declare module '@fingerprintjs/fingerprintjs-pro-react' {
  export interface FpjsProviderOptions {
    loadOptions: {
      apiKey: string;
    };
    children?: React.ReactNode;
    onError?: (err: Error) => void;
  }

  export const FpjsProvider: React.FC<FpjsProviderOptions>;
}

declare module '@copilotkit/shared' {
  export interface UseCoagentReturnType<T> {
    id: string;
    name: string;
    description: string;
    systemMessage: string;
    data: T;
    isLoading: boolean;
    error: Error | null;
  }
}

declare module '@copilotkit/react-coagents' {
  import { UseCoagentReturnType } from '@copilotkit/shared';

  export function useCoagent<T>(options: {
    id: string;
    name: string;
    description: string;
    systemMessage: string;
  }): UseCoagentReturnType<T>;
}
