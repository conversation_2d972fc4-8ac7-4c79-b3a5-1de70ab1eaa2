import { CopilotChat } from "@copilotkit/react-ui";
import { useCopilotContext } from "@copilotkit/react-core";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface CaseContextResearchProps {
  caseId: string;
  caseTitle: string;
  caseType?: string;
  jurisdiction?: string;
  documents?: unknown[];
}

export default function CaseContextResearch({
  caseId,
  caseTitle,
  caseType = "Personal Injury",
  jurisdiction = "Texas",
  documents = []
}: CaseContextResearchProps) {
  // Explicitly type the return from useCopilotContext
  const context = useCopilotContext();

  // Simple informational logging when component mounts
  React.useEffect(() => {
    // Log case information for debugging purposes
    console.log("Case context for research:", {
      caseId,
      caseTitle,
      caseType,
      jurisdiction,
      documentCount: documents?.length || 0
    });
  }, [caseId, caseTitle, caseType, jurisdiction, documents]);

  return (
    <Card className="w-full shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">
          <span>Case-Specific Legal Research</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Using the component compatible with your existing setup */}
          <div className="min-h-[300px] w-full border rounded-md p-4">
            <p className="mb-4 text-sm text-muted-foreground">
              Researching for: {caseTitle} ({caseType} case in {jurisdiction})
            </p>
            {/* @ts-expect-error - Ignoring TypeScript errors for CopilotKit props that aren't in type definitions */}
            <CopilotChat
              className="h-full w-full"
              context={{
                /* For the user's existing CopilotKit configuration which reads agent from context */
                agentType: "research_agent",
                caseId,
                caseTitle,
                caseType,
                jurisdiction,
                documentCount: documents?.length || 0
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
