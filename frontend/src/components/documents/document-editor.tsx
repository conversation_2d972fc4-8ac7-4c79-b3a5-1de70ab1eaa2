'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Edit, CheckCircle, Lightbulb, Loader2, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from '@/lib/utils';

interface DocumentEditorProps {
  sections: {
    title: string;
    content: string;
    isEditable: boolean;
  }[];
  variables?: Record<string, unknown>;
  onUpdateSection: (sectionTitle: string, newContent: string) => void;
  onRequestSuggestion: (sectionTitle: string, sectionContent: string) => void;
  onRequestSuggestionForSelection?: (
    sectionTitle: string,
    sectionContent: string,
    selectedText: string,
    textareaElement: HTMLTextAreaElement
  ) => void;
  activeSuggestion: { sectionTitle: string; suggestion: string } | null;
  isSuggesting: boolean;
  clearSuggestion: () => void;
}

export function DocumentEditor({
  sections,
  variables = {},
  onUpdateSection,
  onRequestSuggestion,
  onRequestSuggestionForSelection,
  activeSuggestion,
  isSuggesting,
  clearSuggestion,
}: DocumentEditorProps) {
  const [editingSections, setEditingSections] = useState<Record<string, boolean>>({});

  const toggleEditing = (sectionTitle: string) => {
    setEditingSections((prev) => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle],
    }));
  };

  const handleContentChange = (sectionTitle: string, content: string) => {
    onUpdateSection(sectionTitle, content);
  };

  const replaceVariables = (content: string): React.ReactNode => {
    if (!variables || Object.keys(variables).length === 0) return content;

    const parts = content.split(/(\{\{[^}]+\}\})/g);

    return parts.map((part, index) => {
      const match = part.match(/\{\{([^}]+)\}\}/);

      if (match) {
        const variableName = match[1];
        const value = variables[variableName];

        if (value !== undefined) {
          return (
            <span
              key={index}
              className="bg-green-100 text-green-800 px-1 rounded border-b border-green-300"
              title={`Variable: ${variableName}`}
            >
              {value}
            </span>
          );
        } else {
          return (
            <span
              key={index}
              className="bg-gray-100 text-gray-500 px-1 rounded border-b border-gray-300"
              title="Unfilled variable"
            >
              {part}
            </span>
          );
        }
      }

      return part;
    });
  };

  const getSelectedText = (textareaElement: HTMLTextAreaElement): string => {
    return textareaElement.value.substring(textareaElement.selectionStart, textareaElement.selectionEnd);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>, sectionTitle: string, sectionContent: string) => {
    if ((event.metaKey || event.ctrlKey) && event.key === '.') {
      event.preventDefault();

      const selectedText = getSelectedText(event.currentTarget);
      console.log('Shortcut detected! Selected text:', selectedText);

      if (onRequestSuggestionForSelection) {
        onRequestSuggestionForSelection(sectionTitle, sectionContent, selectedText, event.currentTarget);
      }
    }
  };

  const handleRequestSuggestion = (sectionTitle: string, sectionContent: string, buttonElement: HTMLButtonElement) => {
    onRequestSuggestion(sectionTitle, sectionContent);
  };

  return (
    <div className="space-y-6">
      {sections.map((section) => (
        <Card key={section.title} className={section.isEditable ? "" : "opacity-70"}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle>{section.title}</CardTitle>

              {section.isEditable && (
                <div className="flex space-x-2">
                  <Popover open={activeSuggestion?.sectionTitle === section.title} onOpenChange={(open) => !open && clearSuggestion()}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled={isSuggesting} // Disable button while any suggestion is loading
                        onClick={(e) => handleRequestSuggestion(section.title, section.content, e.currentTarget as HTMLButtonElement)}
                      >
                        {isSuggesting && activeSuggestion?.sectionTitle === section.title ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Lightbulb className="h-4 w-4 mr-1" />
                        )}
                        Improve
                      </Button>
                    </PopoverTrigger>
                    {activeSuggestion?.sectionTitle === section.title && (
                      <PopoverContent className="w-80">
                        <div className="grid gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium leading-none">Suggestion</h4>
                            <p className="text-sm text-muted-foreground">
                              {activeSuggestion.suggestion}
                            </p>
                          </div>
                          <Button variant="outline" size="sm" onClick={clearSuggestion}>
                            <X className="mr-2 h-4 w-4" /> Dismiss
                          </Button>
                        </div>
                      </PopoverContent>
                    )}
                  </Popover>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleEditing(section.title)}
                  >
                    {editingSections[section.title] ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Done
                      </>
                    ) : (
                      <>
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            {section.isEditable && (
              <CardDescription>
                {editingSections[section.title]
                  ? "Edit this section and click 'Done' when finished"
                  : "This section contains essential information for your document"}
              </CardDescription>
            )}
          </CardHeader>

          <CardContent>
            {editingSections[section.title] ? (
              <Textarea
                value={section.content}
                onChange={(e) => handleContentChange(section.title, e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, section.title, section.content)}
                className="min-h-[200px] font-mono text-sm"
              />
            ) : (
              <div className="prose max-w-none whitespace-pre-wrap">
                {replaceVariables(section.content)}
              </div>
            )}
          </CardContent>

          {editingSections[section.title] && (
            <CardFooter className="pt-0 text-xs text-muted-foreground">
              Variables are in {"{{"}variable_name{"}}"}  format and will be replaced with actual values
            </CardFooter>
          )}
        </Card>
      ))}
    </div>
  );
}
