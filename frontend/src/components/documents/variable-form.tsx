'use client'

import React, { useState, useEffect, useRef } from 'react'
import { CheckCircle, Loader2, AlertCircle, Save, Edit2, X } from 'lucide-react'
import { format, parse, isValid } from 'date-fns'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { DatePicker } from '@/components/ui/date-picker'

interface VariableFormProps {
  variables: Record<string, unknown>
  missingVariables: string[]
  currentVariable?: string | null
  onSubmit: (variable: string, value: any) => void
  updateStatus?: {
    variable: string
    status: 'idle' | 'saving' | 'success' | 'error'
    timestamp: number
  }
}

export function VariableForm({
  variables,
  missingVariables,
  currentVariable,
  onSubmit,
  updateStatus = { variable: '', status: 'idle', timestamp: 0 }
}: VariableFormProps) {
  const [currentValue, setCurrentValue] = useState('')
  const [localValues, setLocalValues] = useState<Record<string, unknown>>({})
  const [editingField, setEditingField] = useState<string | null>(null)
  const [dateValues, setDateValues] = useState<Record<string, Date | undefined>>({})
  const inputRefs = useRef<Record<string, HTMLInputElement | HTMLTextAreaElement>>({})
  const submitTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize local values from props when they change
  useEffect(() => {
    const newLocalValues = { ...localValues }
    Object.entries(variables).forEach(([key, value]) => {
      if (newLocalValues[key] === undefined) {
        newLocalValues[key] = value
      }

      // Try to parse date strings into Date objects for date fields
      if (isDateField(key) && typeof value === 'string' && value) {
        try {
          // Try to parse the date string
          const parsedDate = parse(value, 'yyyy-MM-dd', new Date());
          if (isValid(parsedDate)) {
            setDateValues(prev => ({
              ...prev,
              [key]: parsedDate
            }));
          }
        } catch (e) {
          console.error(`Failed to parse date for ${key}:`, e);
        }
      }
    })
    setLocalValues(newLocalValues)
  }, [variables])

  const handleSubmit = (variable: string) => {
    let value = localValues[variable];

    // Format date values before submitting
    if (isDateField(variable) && dateValues[variable]) {
      value = format(dateValues[variable]!, 'yyyy-MM-dd');
    }

    if (value && value !== variables[variable]) {
      onSubmit(variable, value)
    }
    setCurrentValue('')

    // If this was an editing field, exit edit mode
    if (editingField === variable) {
      setEditingField(null)
    }
  }

  const handleLocalChange = (variable: string, value: string) => {
    // Update local state immediately
    setLocalValues(prev => ({
      ...prev,
      [variable]: value
    }));

    // Clear any existing timeout
    if (submitTimeoutRef.current) {
      clearTimeout(submitTimeoutRef.current);
    }
  }

  const handleDateChange = (variable: string, date: Date | undefined) => {
    // Update the date value
    setDateValues(prev => ({
      ...prev,
      [variable]: date
    }))

    // If date is valid, format and update local values
    if (date) {
      const formattedDate = format(date, 'yyyy-MM-dd')
      setLocalValues(prev => ({
        ...prev,
        [variable]: formattedDate
      }))

      // Handle submission immediately since date picker is a deliberate action
      if (formattedDate !== variables[variable]) {
        onSubmit(variable, formattedDate)
      }
    }
  }

  const handleBlur = (variable: string) => {
    const value = localValues[variable];

    // Submit if the field is not empty and different from original
    if (value && value.trim() !== '' && value !== variables[variable]) {
      onSubmit(variable, value);
    }

    // Clear any pending timeout
    if (submitTimeoutRef.current) {
      clearTimeout(submitTimeoutRef.current);
      submitTimeoutRef.current = null;
    }
  }

  // Function to format variable name for display
  const formatVariableName = (name: string) => {
    return name
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  // Determine if a variable is a date field
  const isDateField = (variable: string) => {
    return variable.toLowerCase().includes('date') ||
           variable.toLowerCase().includes('deadline') ||
           variable.toLowerCase().includes('birthday')
  }

  // Determine if a variable is a large text field
  const isLargeField = (variable: string) => {
    return variable.includes('description') ||
           variable.includes('address') ||
           variable.includes('content')
  }

  // Highlight the current variable requested by the agent
  const isHighlighted = (variable: string) => {
    return currentVariable === variable
  }

  // Start editing a completed field
  const startEditing = (variable: string) => {
    setEditingField(variable)
    // Focus on the input after it renders
    setTimeout(() => {
      const input = inputRefs.current[variable]
      if (input) {
        input.focus()
      }
    }, 10)
  }

  // Cancel editing a field
  const cancelEditing = (variable: string) => {
    // Reset local value to the original value
    setLocalValues(prev => ({
      ...prev,
      [variable]: variables[variable]
    }))

    // Reset date value if it's a date field
    if (isDateField(variable) && variables[variable]) {
      try {
        const parsedDate = parse(variables[variable], 'yyyy-MM-dd', new Date());
        if (isValid(parsedDate)) {
          setDateValues(prev => ({
            ...prev,
            [variable]: parsedDate
          }));
        }
      } catch (e) {
        console.error(`Failed to parse date for ${variable}:`, e);
        setDateValues(prev => ({
          ...prev,
          [variable]: undefined
        }));
      }
    }

    setEditingField(null)
  }

  // Render status indicator for a variable
  const renderStatusIndicator = (variable: string) => {
    if (updateStatus.variable !== variable) return null;

    switch (updateStatus.status) {
      case 'saving':
        return (
          <div className="flex items-center text-muted-foreground animate-pulse ml-1">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            <span className="text-xs">Saving...</span>
          </div>
        );
      case 'success':
        return (
          <div className="flex items-center text-green-500 ml-1">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span className="text-xs">Saved</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center text-red-500 ml-1">
            <AlertCircle className="h-3 w-3 mr-1" />
            <span className="text-xs">Error</span>
          </div>
        );
      default:
        return null;
    }
  };

  // Render the appropriate input field based on variable type
  const renderInputField = (variable: string) => {
    if (isDateField(variable)) {
      return (
        <DatePicker
          date={dateValues[variable]}
          setDate={(date) => handleDateChange(variable, date)}
          placeholder={`Select ${formatVariableName(variable)}`}
        />
      );
    } else if (isLargeField(variable)) {
      return (
        <Textarea
          id={variable}
          ref={(el) => { if (el) inputRefs.current[variable] = el; return undefined; }}
          value={localValues[variable] || ''}
          placeholder={`Enter ${formatVariableName(variable)}`}
          onChange={(e) => handleLocalChange(variable, e.target.value)}
          onBlur={() => handleBlur(variable)}
          className="mt-1"
        />
      );
    } else {
      return (
        <Input
          id={variable}
          ref={(el) => { if (el) inputRefs.current[variable] = el; return undefined; }}
          value={localValues[variable] || ''}
          placeholder={`Enter ${formatVariableName(variable)}`}
          onChange={(e) => handleLocalChange(variable, e.target.value)}
          onBlur={() => handleBlur(variable)}
          className="mt-1"
        />
      );
    }
  };

  // Render completed fields section
  const renderCompletedFields = () => {
    // Get all variables that have values (not just those that aren't in missingVariables)
    const completedVariables = Object.entries(variables)
      .filter(([_, value]) => value !== undefined && value !== '');

    // Always render completed fields section, even if empty
    return (
      <div className="mb-6 p-3 bg-muted/20 rounded-md">
        <h3 className="text-sm font-medium mb-2">Completed Fields</h3>
        <div className="space-y-2">
          {completedVariables.length > 0 ? (
            completedVariables.map(([key, value]) => (
              <div key={key} className="group">
                {editingField === key ? (
                  // Editing mode
                  <div className="flex flex-col space-y-2 p-2 bg-background rounded-md border">
                    <div className="flex items-center justify-between">
                      <Label className="font-medium text-sm">{formatVariableName(key)}</Label>
                      {renderStatusIndicator(key)}
                    </div>

                    {isDateField(key) ? (
                      <DatePicker
                        date={dateValues[key]}
                        setDate={(date) => handleDateChange(key, date)}
                        placeholder={`Select ${formatVariableName(key)}`}
                      />
                    ) : isLargeField(key) ? (
                      <Textarea
                        ref={(el) => { if (el) inputRefs.current[key] = el; return undefined; }}
                        value={localValues[key] || ''}
                        onChange={(e) => handleLocalChange(key, e.target.value)}
                        className="min-h-[80px]"
                        onBlur={() => handleBlur(key)}
                      />
                    ) : (
                      <Input
                        ref={(el) => { if (el) inputRefs.current[key] = el; return undefined; }}
                        value={localValues[key] || ''}
                        onChange={(e) => handleLocalChange(key, e.target.value)}
                        onBlur={() => handleBlur(key)}
                      />
                    )}

                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="default"
                        className="flex-1"
                        onClick={() => handleSubmit(key)}
                      >
                        <Save className="h-3 w-3 mr-1" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => cancelEditing(key)}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div className="flex items-center justify-between text-sm text-muted-foreground p-1 rounded-md hover:bg-muted/30 transition-colors">
                    <div className="flex items-center flex-1 overflow-hidden">
                      <CheckCircle className="h-3 w-3 mr-2 text-green-500 flex-shrink-0" />
                      <span className="font-medium">{formatVariableName(key)}:</span>
                      <span className="ml-1 truncate">{value.toString()}</span>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                      onClick={() => startEditing(key)}
                    >
                      <Edit2 className="h-3 w-3" />
                      <span className="sr-only">Edit</span>
                    </Button>
                  </div>
                )}
              </div>
            ))
          ) : (
            <p className="text-sm text-muted-foreground italic">
              No fields completed yet
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Always show completed fields at the top */}
      {renderCompletedFields()}

      {currentVariable && missingVariables.includes(currentVariable) ? (
        // Show focused input for the current variable
        <div className="space-y-2 border p-4 rounded-md bg-muted/30">
          <div className="flex items-center justify-between">
            <Label className="font-medium">{formatVariableName(currentVariable)}</Label>
            {renderStatusIndicator(currentVariable)}
          </div>

          {isDateField(currentVariable) ? (
            <DatePicker
              date={dateValues[currentVariable]}
              setDate={(date) => handleDateChange(currentVariable, date)}
              placeholder={`Select ${formatVariableName(currentVariable)}`}
            />
          ) : isLargeField(currentVariable) ? (
            <Textarea
              ref={(el) => { if (el) inputRefs.current[currentVariable] = el; return undefined; }}
              value={localValues[currentVariable] || ''}
              onChange={(e) => handleLocalChange(currentVariable, e.target.value)}
              placeholder={`Enter ${formatVariableName(currentVariable)}`}
              className="min-h-[100px]"
              onBlur={() => handleBlur(currentVariable)}
            />
          ) : (
            <Input
              ref={(el) => { if (el) inputRefs.current[currentVariable] = el; return undefined; }}
              value={localValues[currentVariable] || ''}
              onChange={(e) => handleLocalChange(currentVariable, e.target.value)}
              placeholder={`Enter ${formatVariableName(currentVariable)}`}
              onBlur={() => handleBlur(currentVariable)}
            />
          )}

          <Button
            className="w-full mt-2"
            onClick={() => handleSubmit(currentVariable)}
          >
            Submit
          </Button>
        </div>
      ) : (
        // Show all missing variables
        <div className="space-y-4">
          {missingVariables.map(variable => (
            <div
              key={variable}
              className={`flex items-start space-x-2 p-2 rounded ${
                isHighlighted(variable) ? 'bg-muted/30 border' : ''
              }`}
            >
              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <Label
                    htmlFor={variable}
                    className={`text-sm font-medium ${isHighlighted(variable) ? 'text-primary' : ''}`}
                  >
                    {formatVariableName(variable)}
                  </Label>
                  {renderStatusIndicator(variable)}
                </div>

                {renderInputField(variable)}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
