/**
 * Standalone Usage Dashboard Component for Testing
 *
 * This is a simplified version of the usage dashboard component
 * that doesn't depend on external services.
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, AlertTriangle } from 'lucide-react';

interface UsageDashboardProps {
  initialUsageData?: unknown[];
  initialQuotaData?: {
    quotaLimit: number;
    currentUsage: number;
    percentUsed: number;
    remainingQuota: number;
  };
}

export function UsageDashboard({
  initialUsageData = [],
  initialQuotaData = { quotaLimit: 100, currentUsage: 50, percentUsed: 50, remainingQuota: 50 }
}: UsageDashboardProps) {
  const [selectedUsageType, setSelectedUsageType] = React.useState('document_upload');
  const [usageData, setUsageData] = React.useState(initialUsageData);
  const [quotaData, setQuotaData] = React.useState(initialQuotaData);
  const [error, setError] = React.useState<string | null>(null);

  // Format usage type for display
  const formatUsageType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Get usage data for the selected type
  const getUsageForType = (type: string) => {
    return usageData.find(usage => usage.usageType === type) || null;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Usage Dashboard</CardTitle>
        <CardDescription>Monitor your resource usage and quotas</CardDescription>
      </CardHeader>

      <CardContent>
        {error && (
          <div className="text-red-500 mb-4">{error}</div>
        )}

        {quotaData && quotaData.percentUsed >= 100 && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4 flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
            <div>
              <p className="font-medium">Quota Exceeded</p>
              <p className="text-sm">You have exceeded your {selectedUsageType.replace('_', ' ')} quota. Please upgrade your plan to continue using this feature.</p>
            </div>
          </div>
        )}

        {quotaData && quotaData.percentUsed >= 80 && quotaData.percentUsed < 100 && (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" />
            <div>
              <p className="font-medium">Approaching Quota Limit</p>
              <p className="text-sm">You are approaching your {selectedUsageType.replace('_', ' ')} quota limit. Consider upgrading your plan soon.</p>
            </div>
          </div>
        )}

        <Tabs defaultValue="document_upload" className="w-full" onValueChange={setSelectedUsageType}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="document_upload">Document Upload</TabsTrigger>
            <TabsTrigger value="document_processing">Document Processing</TabsTrigger>
            <TabsTrigger value="api_calls">API Calls</TabsTrigger>
          </TabsList>

          {['document_upload', 'document_processing', 'api_calls'].map((usageType) => (
            <TabsContent key={usageType} value={usageType} className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">{formatUsageType(usageType)} Usage</h3>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Usage</span>
                      <span>{quotaData.currentUsage} / {quotaData.quotaLimit}</span>
                    </div>
                    <Progress
                      value={quotaData.percentUsed}
                      className={`h-2 ${
                        quotaData.percentUsed >= 100 ? 'bg-red-200' :
                        quotaData.percentUsed >= 80 ? 'bg-yellow-200' :
                        'bg-gray-200'
                      }`}
                      indicatorClassName={`${
                        quotaData.percentUsed >= 100 ? 'bg-red-500' :
                        quotaData.percentUsed >= 80 ? 'bg-yellow-500' :
                        ''
                      }`}
                    />
                    <div className={`text-xs ${
                      quotaData.percentUsed >= 100 ? 'text-red-600 font-medium' :
                      quotaData.percentUsed >= 80 ? 'text-yellow-600 font-medium' :
                      'text-gray-500'
                    }`}>
                      {quotaData.percentUsed >= 100 ? (
                        <span>Quota exceeded! {quotaData.percentUsed}% used</span>
                      ) : quotaData.percentUsed >= 80 ? (
                        <span>Approaching limit! {quotaData.percentUsed}% used</span>
                      ) : (
                        <span>{quotaData.percentUsed}% of quota used</span>
                      )}
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="font-medium mb-2">Usage Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Current Usage:</span>
                        <span>{quotaData.currentUsage} units</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Quota Limit:</span>
                        <span>{quotaData.quotaLimit} units</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Remaining:</span>
                        <span>{quotaData.remainingQuota} units</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}
