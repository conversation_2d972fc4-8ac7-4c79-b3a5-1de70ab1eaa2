/**
 * Anomaly detection service for security monitoring
 * Detects unusual patterns in user behavior and security events
 */

import { createClient } from '@supabase/supabase-js';

export interface AnomalyScore {
  score: number;        // 0-100 score, higher means more suspicious
  reasons: string[];    // Reasons for the score
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId: string;
  eventId: string;
  timestamp: string;
}

export interface UserBehaviorProfile {
  userId: string;
  commonLocations: string[];
  commonIpAddresses: string[];
  commonDevices: string[];
  commonBrowsers: string[];
  commonLoginTimes: number[]; // Hours of day (0-23)
  commonLoginDays: number[];  // Days of week (0-6, 0 = Sunday)
  lastLogin: string;
  averageSessionDuration: number;
}

/**
 * Analyze a security event for anomalies
 * @param eventType Type of security event
 * @param details Event details
 * @param userId User ID
 * @returns Anomaly score and reasons
 */
export async function analyzeSecurityEvent(
  eventType: string,
  details: Record<string, unknown>,
  userId: string
): Promise<AnomalyScore> {
  // Initialize with a base score
  let score = 0;
  const reasons: string[] = [];

  // Get user behavior profile
  const profile = await getUserBehaviorProfile(userId);

  // Check for location anomalies
  if (details.geolocation) {
    const { country_name, city } = details.geolocation;
    const location = `${city}, ${country_name}`;

    if (profile.commonLocations.length > 0 && !profile.commonLocations.includes(location)) {
      score += 25;
      reasons.push(`Unusual location: ${location}`);
    }
  }

  // Check for IP address anomalies
  if (details.ip_address && profile.commonIpAddresses.length > 0) {
    if (!profile.commonIpAddresses.includes(details.ip_address)) {
      score += 15;
      reasons.push(`Unusual IP address: ${details.ip_address}`);
    }
  }

  // Check for device/browser anomalies
  if (details.userAgent) {
    const browser = getBrowserFromUserAgent(details.userAgent);
    const device = getDeviceFromUserAgent(details.userAgent);

    if (profile.commonBrowsers.length > 0 && !profile.commonBrowsers.includes(browser)) {
      score += 10;
      reasons.push(`Unusual browser: ${browser}`);
    }

    if (profile.commonDevices.length > 0 && !profile.commonDevices.includes(device)) {
      score += 15;
      reasons.push(`Unusual device: ${device}`);
    }
  }

  // Check for time anomalies
  const eventTime = new Date(details.timestamp || new Date());
  const hour = eventTime.getHours();
  const day = eventTime.getDay();

  if (profile.commonLoginTimes.length > 0 && !profile.commonLoginTimes.includes(hour)) {
    score += 20;
    reasons.push(`Unusual login time: ${formatHour(hour)}`);
  }

  if (profile.commonLoginDays.length > 0 && !profile.commonLoginDays.includes(day)) {
    score += 10;
    reasons.push(`Unusual login day: ${formatDay(day)}`);
  }

  // Check for rapid succession logins
  if (profile.lastLogin) {
    const lastLoginTime = new Date(profile.lastLogin).getTime();
    const currentTime = eventTime.getTime();
    const timeDiff = currentTime - lastLoginTime;

    // If less than 5 minutes between logins from different locations
    if (timeDiff < 5 * 60 * 1000 && details.geolocation) {
      score += 30;
      reasons.push('Rapid succession login from different location');
    }
  }

  // Check for failed login attempts
  if (eventType === 'auth.login' && details.success === false) {
    // Get recent failed login attempts
    const recentFailures = await getRecentFailedLogins(userId);

    if (recentFailures > 3) {
      score += 25;
      reasons.push(`Multiple failed login attempts: ${recentFailures}`);
    }
  }

  // Determine severity based on score
  let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
  if (score >= 75) {
    severity = 'critical';
  } else if (score >= 50) {
    severity = 'high';
  } else if (score >= 25) {
    severity = 'medium';
  }

  return {
    score,
    reasons,
    severity,
    userId,
    eventId: details.eventId || 'unknown',
    timestamp: details.timestamp || new Date().toISOString()
  };
}

/**
 * Get a user's behavior profile based on past activity
 * @param userId User ID
 * @returns User behavior profile
 */
async function getUserBehaviorProfile(userId: string): Promise<UserBehaviorProfile> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the user's past login events
    const { data: events } = await supabase
      .schema('security')
      .from('events')
      .select('*')
      .eq('user_id', userId)
      .eq('event_type', 'auth.login')
      .eq('event_category', 'authentication')
      .order('created_at', { ascending: false })
      .limit(50);

    if (!events || events.length === 0) {
      // Return an empty profile if no events found
      return {
        userId,
        commonLocations: [],
        commonIpAddresses: [],
        commonDevices: [],
        commonBrowsers: [],
        commonLoginTimes: [],
        commonLoginDays: [],
        lastLogin: '',
        averageSessionDuration: 0
      };
    }

    // Extract locations, IPs, devices, browsers, and times
    const locations: string[] = [];
    const ipAddresses: string[] = [];
    const devices: string[] = [];
    const browsers: string[] = [];
    const loginTimes: number[] = [];
    const loginDays: number[] = [];

    events.forEach(event => {
      // Extract location
      if (event.location_city && event.location_country) {
        const location = `${event.location_city}, ${event.location_country}`;
        if (!locations.includes(location)) {
          locations.push(location);
        }
      }

      // Extract IP address
      if (event.ip_address && !ipAddresses.includes(event.ip_address)) {
        ipAddresses.push(event.ip_address);
      }

      // Extract device and browser from user agent
      if (event.user_agent) {
        const browser = getBrowserFromUserAgent(event.user_agent);
        const device = getDeviceFromUserAgent(event.user_agent);

        if (browser && !browsers.includes(browser)) {
          browsers.push(browser);
        }

        if (device && !devices.includes(device)) {
          devices.push(device);
        }
      }

      // Extract login time and day
      const eventTime = new Date(event.created_at);
      const hour = eventTime.getHours();
      const day = eventTime.getDay();

      if (!loginTimes.includes(hour)) {
        loginTimes.push(hour);
      }

      if (!loginDays.includes(day)) {
        loginDays.push(day);
      }
    });

    return {
      userId,
      commonLocations: locations,
      commonIpAddresses: ipAddresses,
      commonDevices: devices,
      commonBrowsers: browsers,
      commonLoginTimes: loginTimes,
      commonLoginDays: loginDays,
      lastLogin: events[0]?.created_at || '',
      averageSessionDuration: 0 // Would need logout events to calculate this
    };
  } catch (err) {
    console.error('Error getting user behavior profile:', err);
    // Return an empty profile in case of error
    return {
      userId,
      commonLocations: [],
      commonIpAddresses: [],
      commonDevices: [],
      commonBrowsers: [],
      commonLoginTimes: [],
      commonLoginDays: [],
      lastLogin: '',
      averageSessionDuration: 0
    };
  }
}

/**
 * Get the number of recent failed login attempts for a user
 * @param userId User ID
 * @returns Number of recent failed login attempts
 */
async function getRecentFailedLogins(userId: string): Promise<number> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get failed login attempts in the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();

    const { data, error } = await supabase
      .schema('security')
      .from('events')
      .select('id')
      .eq('user_id', userId)
      .eq('event_type', 'auth.login')
      .eq('event_category', 'authentication')
      .gte('created_at', oneHourAgo)
      .filter('details->success', 'eq', false);

    if (error) {
      console.error('Error getting recent failed logins:', error);
      return 0;
    }

    return data?.length || 0;
  } catch (err) {
    console.error('Error getting recent failed logins:', err);
    return 0;
  }
}

/**
 * Extract browser information from user agent string
 * @param userAgent User agent string
 * @returns Browser name
 */
function getBrowserFromUserAgent(userAgent: string): string {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('MSIE') || userAgent.includes('Trident')) return 'Internet Explorer';
  return 'Unknown';
}

/**
 * Extract device information from user agent string
 * @param userAgent User agent string
 * @returns Device type
 */
function getDeviceFromUserAgent(userAgent: string): string {
  if (userAgent.includes('iPhone')) return 'iPhone';
  if (userAgent.includes('iPad')) return 'iPad';
  if (userAgent.includes('Android') && userAgent.includes('Mobile')) return 'Android Phone';
  if (userAgent.includes('Android')) return 'Android Tablet';
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Macintosh')) return 'Mac';
  if (userAgent.includes('Linux')) return 'Linux';
  return 'Unknown';
}

/**
 * Format hour for display
 * @param hour Hour (0-23)
 * @returns Formatted hour string
 */
function formatHour(hour: number): string {
  if (hour === 0) return '12 AM';
  if (hour < 12) return `${hour} AM`;
  if (hour === 12) return '12 PM';
  return `${hour - 12} PM`;
}

/**
 * Format day for display
 * @param day Day of week (0-6, 0 = Sunday)
 * @returns Formatted day string
 */
function formatDay(day: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[day];
}

/**
 * Log an anomaly to the security events table
 * @param anomaly Anomaly score and details
 */
export async function logAnomalyEvent(anomaly: AnomalyScore): Promise<void> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Log the anomaly as a security event
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'suspicious.anomaly_detected',
        event_category: 'suspicious',
        user_id: anomaly.userId,
        details: {
          score: anomaly.score,
          reasons: anomaly.reasons,
          severity: anomaly.severity,
          relatedEventId: anomaly.eventId
        },
        created_at: new Date().toISOString()
      });
  } catch (err) {
    console.error('Error logging anomaly event:', err);
  }
}
