/**
 * User and Firm Data Mapper
 *
 * This module provides functions for mapping between database user/firm records and domain models.
 * It handles the core entities for authentication and tenant isolation.
 */
import { z } from "zod";
import { Database } from "../supabase/database.types";

// Type aliases for better readability
export type UserRow = Database["tenants"]["Tables"]["users"]["Row"];
export type UserInsert = Database["tenants"]["Tables"]["users"]["Insert"];
export type UserUpdate = Database["tenants"]["Tables"]["users"]["Update"];

export type FirmRow = Database["tenants"]["Tables"]["firms"]["Row"];
export type FirmInsert = Database["tenants"]["Tables"]["firms"]["Insert"];
export type FirmUpdate = Database["tenants"]["Tables"]["firms"]["Update"];

// Domain model types for Users
export type UserRole = "partner" | "attorney" | "paralegal" | "staff" | "client" | "admin";
export type FirmRole = "administrator" | "partner" | "attorney" | "paralegal" | "staff";

// User domain model
export interface User {
  id: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  role: UserRole;
  firmRole?: FirmRole | null;
  tenantId: string;
  authUserId?: string | null;
  avatarUrl?: string | null;
  lastLogin?: string | null;
  settings?: Record<string, unknown> | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  // Expanded relationships
  firm?: Firm | null;
}

// Firm domain model
export interface Firm {
  id: string;
  tenantId: string;
  name: string;
  stateBarNumber: string;
  firmType: string;
  primaryEmail: string;
  secondaryEmail?: string | null;
  phone: string;
  fax?: string | null;
  websiteUrl?: string | null;
  address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  practiceAreas?: string[];
  specializations?: string[];
  adminUserId?: string | null;
  status: string;
  verificationStatus: string;
  subscriptionTier: string;
  subscriptionStatus: string;
  taxId?: string | null;
  yearEstablished?: number | null;
  settings?: Record<string, unknown> | null;
  metadata?: Record<string, unknown> | null;
  jurisdictionSettings?: Record<string, unknown> | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  // Expanded relationships
  adminUser?: User | null;
  users?: User[];
}

// User create input model
export interface CreateUserDto {
  email: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  firmRole?: FirmRole;
  tenantId: string;
  authUserId?: string;
  avatarUrl?: string;
  settings?: Record<string, unknown>;
}

// User update input model
export interface UpdateUserDto extends Partial<CreateUserDto> {
  lastLogin?: string;
}

// Firm create input model
export interface CreateFirmDto {
  name: string;
  stateBarNumber: string;
  firmType: string;
  primaryEmail: string;
  secondaryEmail?: string;
  phone: string;
  fax?: string;
  websiteUrl?: string;
  address: Record<string, unknown>;
  practiceAreas?: string[];
  specializations?: string[];
  adminUserId?: string;
  subscriptionTier?: string;
  taxId?: string;
  yearEstablished?: number;
  settings?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  jurisdictionSettings?: Record<string, unknown>;
}

// Firm update input model
export interface UpdateFirmDto extends Partial<CreateFirmDto> {
  status?: string;
  verificationStatus?: string;
  subscriptionStatus?: string;
}

// Zod schema for user validation
export const createUserSchema = z.object({
  email: z.string().email("Valid email is required"),
  firstName: z.string().optional().nullable(),
  lastName: z.string().optional().nullable(),
  role: z.enum(["partner", "attorney", "paralegal", "staff", "client", "admin"]),
  firmRole: z.enum(["administrator", "partner", "attorney", "paralegal", "staff"]).optional().nullable(),
  tenantId: z.string().uuid(),
  authUserId: z.string().optional().nullable(),
  avatarUrl: z.string().url().optional().nullable(),
  settings: z.record(z.any()).optional().nullable(),
});

export const updateUserSchema = createUserSchema.partial().extend({
  lastLogin: z.string().optional(),
});

// Zod schema for firm validation
export const createFirmSchema = z.object({
  name: z.string().min(1, "Firm name is required"),
  stateBarNumber: z.string().min(1, "State bar number is required"),
  firmType: z.string().min(1, "Firm type is required"),
  primaryEmail: z.string().email("Valid primary email is required"),
  secondaryEmail: z.string().email().optional().nullable(),
  phone: z.string().min(1, "Phone number is required"),
  fax: z.string().optional().nullable(),
  websiteUrl: z.string().url().optional().nullable(),
  address: z.record(z.any()),
  practiceAreas: z.array(z.string()).optional().nullable(),
  specializations: z.array(z.string()).optional().nullable(),
  adminUserId: z.string().uuid().optional().nullable(),
  taxId: z.string().optional().nullable(),
  yearEstablished: z.number().optional().nullable(),
  settings: z.record(z.any()).optional().nullable(),
  metadata: z.record(z.any()).optional().nullable(),
  jurisdictionSettings: z.record(z.any()).optional().nullable(),
  subscriptionTier: z.string().default("basic"),
});

export const updateFirmSchema = createFirmSchema.partial().extend({
  status: z.string().optional(),
  verificationStatus: z.string().optional(),
  subscriptionStatus: z.string().optional(),
});

/**
 * Maps a database user row to a domain user object
 */
export function mapUserRowToUser(row: UserRow): User {
  return {
    id: row.id,
    email: row.email,
    firstName: row.first_name,
    lastName: row.last_name,
    role: row.role as UserRole,
    firmRole: row.firm_role as FirmRole | null,
    tenantId: row.tenant_id,
    authUserId: row.auth_user_id,
    avatarUrl: row.avatar_url,
    lastLogin: row.last_login,
    settings: typeof row.settings === 'string' ? JSON.parse(row.settings) : row.settings,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
    // Relations are added outside this mapper if they're included in the query
    firm: null
  };
}

/**
 * Maps a database firm row to a domain firm object
 */
export function mapFirmRowToFirm(row: FirmRow): Firm {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    name: row.name,
    stateBarNumber: row.state_bar_number,
    firmType: row.firm_type,
    primaryEmail: row.primary_email,
    secondaryEmail: row.secondary_email,
    phone: row.phone,
    fax: row.fax,
    websiteUrl: row.website_url,
    address: typeof row.address === 'string' ? JSON.parse(row.address) : row.address,
    practiceAreas: row.practice_areas ?? undefined,
    specializations: row.specializations ?? undefined,
    adminUserId: row.admin_user_id,
    status: row.status,
    verificationStatus: row.verification_status,
    subscriptionTier: row.subscription_tier,
    subscriptionStatus: row.subscription_status,
    taxId: row.tax_id,
    yearEstablished: row.year_established,
    settings: typeof row.settings === 'string' ? JSON.parse(row.settings) : row.settings,
    metadata: typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata,
    jurisdictionSettings: typeof row.jurisdiction_settings === 'string' ? JSON.parse(row.jurisdiction_settings) : row.jurisdiction_settings,
    createdAt: row.created_at,
    updatedAt: row.updated_at,
    // Relations are added outside this mapper if they're included in the query
    adminUser: null,
    users: []
  };
}

/**
 * Maps a domain user create DTO to a database user insert object
 */
export function mapCreateUserDtoToUserInsert(dto: CreateUserDto): UserInsert {
  return {
    email: dto.email,
    first_name: dto.firstName,
    last_name: dto.lastName,
    role: dto.role,
    firm_role: dto.firmRole,
    tenant_id: dto.tenantId,
    auth_user_id: dto.authUserId,
    avatar_url: dto.avatarUrl,
    settings: dto.settings
  };
}

/**
 * Maps a domain user update DTO to a database user update object
 */
export function mapUpdateUserDtoToUserUpdate(dto: UpdateUserDto): UserUpdate {
  const update: UserUpdate = {};

  if (dto.email !== undefined) update.email = dto.email;
  if (dto.firstName !== undefined) update.first_name = dto.firstName;
  if (dto.lastName !== undefined) update.last_name = dto.lastName;
  if (dto.role !== undefined) update.role = dto.role;
  if (dto.firmRole !== undefined) update.firm_role = dto.firmRole;
  if (dto.tenantId !== undefined) update.tenant_id = dto.tenantId;
  if (dto.authUserId !== undefined) update.auth_user_id = dto.authUserId;
  if (dto.avatarUrl !== undefined) update.avatar_url = dto.avatarUrl;
  if (dto.settings !== undefined) update.settings = dto.settings;
  if (dto.lastLogin !== undefined) update.last_login = dto.lastLogin;

  // Always update timestamp
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Maps a domain firm create DTO to a database firm insert object
 */
export function mapCreateFirmDtoToFirmInsert(dto: CreateFirmDto, tenantId: string): FirmInsert {
  return {
    name: dto.name,
    state_bar_number: dto.stateBarNumber,
    firm_type: dto.firmType,
    primary_email: dto.primaryEmail,
    secondary_email: dto.secondaryEmail,
    phone: dto.phone,
    fax: dto.fax,
    website_url: dto.websiteUrl,
    address: dto.address,
    practice_areas: dto.practiceAreas,
    specializations: dto.specializations,
    admin_user_id: dto.adminUserId,
    subscription_tier: dto.subscriptionTier || 'basic',
    tax_id: dto.taxId,
    year_established: dto.yearEstablished,
    settings: dto.settings,
    metadata: dto.metadata,
    jurisdiction_settings: dto.jurisdictionSettings,
    tenant_id: tenantId,
    status: 'active',
    verification_status: 'pending',
    subscription_status: 'active'
  };
}

/**
 * Maps a domain firm update DTO to a database firm update object
 */
export function mapUpdateFirmDtoToFirmUpdate(dto: UpdateFirmDto): FirmUpdate {
  const update: FirmUpdate = {};

  if (dto.name !== undefined) update.name = dto.name;
  if (dto.stateBarNumber !== undefined) update.state_bar_number = dto.stateBarNumber;
  if (dto.firmType !== undefined) update.firm_type = dto.firmType;
  if (dto.primaryEmail !== undefined) update.primary_email = dto.primaryEmail;
  if (dto.secondaryEmail !== undefined) update.secondary_email = dto.secondaryEmail;
  if (dto.phone !== undefined) update.phone = dto.phone;
  if (dto.fax !== undefined) update.fax = dto.fax;
  if (dto.websiteUrl !== undefined) update.website_url = dto.websiteUrl;
  if (dto.address !== undefined) update.address = dto.address;
  if (dto.practiceAreas !== undefined) update.practice_areas = dto.practiceAreas;
  if (dto.specializations !== undefined) update.specializations = dto.specializations;
  if (dto.adminUserId !== undefined) update.admin_user_id = dto.adminUserId;
  if (dto.status !== undefined) update.status = dto.status;
  if (dto.verificationStatus !== undefined) update.verification_status = dto.verificationStatus;
  if (dto.subscriptionTier !== undefined) update.subscription_tier = dto.subscriptionTier;
  if (dto.subscriptionStatus !== undefined) update.subscription_status = dto.subscriptionStatus;
  if (dto.taxId !== undefined) update.tax_id = dto.taxId;
  if (dto.yearEstablished !== undefined) update.year_established = dto.yearEstablished;
  if (dto.settings !== undefined) update.settings = dto.settings;
  if (dto.metadata !== undefined) update.metadata = dto.metadata;
  if (dto.jurisdictionSettings !== undefined) update.jurisdiction_settings = dto.jurisdictionSettings;

  // Always update timestamp
  update.updated_at = new Date().toISOString();

  return update;
}

/**
 * Maps database user rows with relations to domain user objects
 */
export function mapUserRowsWithRelations(
  rows: Array<UserRow & { firm?: FirmRow }>,
): User[] {
  return rows.map(row => {
    const user = mapUserRowToUser(row);

    // Map the relations if they exist
    if (row.firm) {
      user.firm = mapFirmRowToFirm(row.firm);
    }

    return user;
  });
}

/**
 * Maps database firm rows with relations to domain firm objects
 */
export function mapFirmRowsWithRelations(
  rows: Array<FirmRow & { admin_user?: UserRow, users?: UserRow[] }>,
): Firm[] {
  return rows.map(row => {
    const firm = mapFirmRowToFirm(row);

    // Map the relations if they exist
    if (row.admin_user) {
      firm.adminUser = mapUserRowToUser(row.admin_user);
    }

    if (row.users && Array.isArray(row.users)) {
      firm.users = row.users.map(mapUserRowToUser);
    }

    return firm;
  });
}

/**
 * Utility function to get the display name of a user
 */
export function getUserDisplayName(user: User): string {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  } else if (user.firstName) {
    return user.firstName;
  } else if (user.lastName) {
    return user.lastName;
  } else {
    return user.email;
  }
}

/**
 * Enhanced User and Firm Validation Utility Methods
 */
export const UserFirmValidation = {
  /**
   * Validates if a user has admin permissions
   */
  isAdmin(user: User): boolean {
    return user.role === 'admin' || user.role === 'partner' || user.firmRole === 'administrator';
  },

  /**
   * Validates if a user has staff-level permissions
   */
  isStaff(user: User): boolean {
    return ['partner', 'attorney', 'paralegal', 'staff'].includes(user.role as string);
  },

  /**
   * Validates if a user can manage other users
   */
  canManageUsers(user: User): boolean {
    return user.role === 'partner' || user.firmRole === 'administrator';
  },

  /**
   * Validates if a firm subscription is active
   */
  hasActiveSubscription(firm: Firm): boolean {
    return firm.subscriptionStatus === 'active';
  },

  /**
   * Gets a standardized representation of a firm's address
   */
  getFormattedAddress(firm: Firm): string {
    const addr = firm.address;
    const parts = [];

    if (addr.street) parts.push(addr.street);
    if (addr.city) parts.push(addr.city);
    if (addr.state) {
      if (addr.zipCode) {
        parts.push(`${addr.state} ${addr.zipCode}`);
      } else {
        parts.push(addr.state);
      }
    } else if (addr.zipCode) {
      parts.push(addr.zipCode);
    }
    if (addr.country) parts.push(addr.country);

    return parts.join(', ');
  }
};
