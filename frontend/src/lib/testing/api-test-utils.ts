import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

/**
 * Check if the request is from a Cypress test
 * @param req The Next.js request object
 */
export function isTestRequest(_req: Request): boolean {
  return req.headers.get('x-cypress-test') === 'true';
}

/**
 * Get a mock session for testing API routes
 */
export function getMockApiSession() {
  return {
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    expires_at: Math.floor(Date.now() / 1000) + 3600,
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      app_metadata: {
        tenant_id: 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11',
        role: 'partner'
      },
      user_metadata: {
        first_name: 'Test',
        last_name: 'User'
      },
      aud: 'authenticated',
      created_at: new Date().toISOString()
    }
  };
}

/**
 * Get a Supabase client for API routes that works with Cypress tests
 */
export function getApiSupabaseClient() {
  // For testing API routes, we use the service key to bypass RLS
  return createClient<Database>(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_KEY!
  );
}
