import type { SupabaseClient } from '@supabase/supabase-js';
import type { Json, Database } from '../supabase/database.types'; // Fixed import path with relative path
import { createServiceClient } from '../supabase/server'; // Fixed import path with relative path
// Using our newly created sanitizer utility
import { sanitizeFilename } from '../utils/filename-sanitizer'; // Fixed import path with relative path
import { z } from 'zod';

// Document metadata schema
export const DocumentMetadataSchema = z.object({
  title: z.string().min(1, 'Document title is required'),
  description: z.string().optional(),
  document_type: z.string().optional(),
  case_id: z.string().uuid('Invalid case ID').optional(),
  client_id: z.string().uuid('Invalid client ID').optional(),
  tags: z.array(z.string()).optional(),
  is_sensitive: z.boolean().optional().default(false),
  status: z.enum(['draft', 'final', 'archived']).default('draft'),
  metadata: z.record(z.any()).optional().default(() => ({})),
});

// Document update schema (all fields optional)
export const UpdateDocumentMetadataSchema = DocumentMetadataSchema.partial();

// Define based on 'authored_documents' table and the select query in getAll
/**
 * Interface for authored documents that matches the database schema
 */
interface AuthoredDocument {
  id: string;
  tenant_id: string;
  case_id: string | null;
  client_id: string | null;
  content: string;
  title: string;
  status: string;
  gcs_path: string | null;
  metadata: Json | null;
  embedding_status: string | null;
  template_id: string | null;
  variables_used: Json | null;
  version: number | null;
  created_at: string | null;
  created_by: string | null;
  updated_at: string | null;
  updated_by: string | null;
  last_embedded_at: string | null;
  sent_at: string | null;
  signed_at: string | null;
  // Relations (joined tables) - using any for flexibility with query results
  case?: any;
  client?: any;
  // Optional fields that may come from joins but aren't in the base schema
  [key: string]: any;
}

// Interface for the structure returned by the getAll method
interface GetAllDocumentsResponse {
  documents: AuthoredDocument[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export class DocumentService {
  private supabase: SupabaseClient<Database>;
  private tenantId: string;

  constructor(supabase: SupabaseClient<Database>, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Get all documents with optional filtering and pagination
   */
  async getAll(options: {
    page?: number;
    limit?: number;
    case_id?: string;
    client_id?: string;
    document_type?: string;
    status?: string;
    searchTerm?: string;
  } = {}): Promise<GetAllDocumentsResponse> {
    const {
      page = 1,
      limit = 10,
      case_id,
      client_id,
      document_type,
      status,
      searchTerm
    } = options;

    let query = this.supabase
      .schema('tenants')
      .from('authored_documents')
      .select(`
        *,
        uploaded_by:users!authored_documents_uploaded_by_fkey(id, email, first_name, last_name),
        case:case_id(id, title),
        client:client_id(id, first_name, last_name, business_name)
      `, { count: 'exact' })
      .eq('tenant_id', this.tenantId);

    // Apply filters - use any to break the chain of generic typing
    // This prevents excessive type recursion depth
    let typedQuery = query as any;

    if (case_id) typedQuery = typedQuery.eq('case_id', case_id);
    if (client_id) typedQuery = typedQuery.eq('client_id', client_id);
    if (document_type) typedQuery = typedQuery.eq('document_type', document_type);
    if (status) typedQuery = typedQuery.eq('status', status);

    // Apply search if provided
    if (searchTerm) {
      typedQuery = typedQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Restore the query reference
    query = typedQuery;

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    // Use the typedQuery to prevent excessive type recursion depth
    typedQuery = typedQuery.range(from, to).order('created_at', { ascending: false });
    query = typedQuery;

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching documents:', error);
      throw error;
    }

    return {
      documents: data || [],
      page,
      limit,
      total: count || 0,
      totalPages: count ? Math.ceil(count / limit) : 0,
    };
  }

  /**
   * Get a single document by ID
   */
  async getById(id: string) {
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .select(`
        *,
        uploaded_by:users!authored_documents_uploaded_by_fkey(id, email, first_name, last_name),
        case:case_id(id, title),
        client:client_id(id, first_name, last_name, business_name)
      `)
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Document not found');
      }
      console.error('Error fetching document:', error);
      throw error;
    }

    return data;
  }

  /**
   * Upload a new document with metadata
   */
  async upload(
    file: File,
    metadata: z.infer<typeof DocumentMetadataSchema>,
    userId: string
  ) {
    // Validate metadata
    const validatedMetadata = DocumentMetadataSchema.parse(metadata);

    // Generate a unique filename
    const timestamp = Date.now();
    const fileExt = file.name.split('.').pop();
    const fileName = `${this.tenantId}/${timestamp}-${file.name.replace(/\s+/g, '_')}`;

    // Upload file to storage
    const { data: uploadData, error: uploadError } = await this.supabase
      .storage
      .from('documents')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (uploadError) {
      console.error('Error uploading document:', uploadError);
      throw uploadError;
    }

    // Get the file URL
    const { data: publicUrlData } = await this.supabase
      .storage
      .from('documents')
      .getPublicUrl(fileName);

    const fileUrl = publicUrlData?.publicUrl;

    // Prepare metadata JSON object, handling optional fields
    const recordMetadata = {
      description: validatedMetadata.description ?? null,
      document_type: validatedMetadata.document_type ?? null,
      tags: validatedMetadata.tags ?? null, // Store tags in metadata JSON
      is_sensitive: validatedMetadata.is_sensitive ?? false, // Store is_sensitive in metadata JSON
      // Add file details to metadata JSON as direct columns don't exist
      original_filename: file.name ?? null,
      file_type: file.type ?? null,
      file_size: file.size ?? null,
      file_url: fileUrl ?? null,
    };

    // Create document metadata record for insertion
    const documentRecord = {
      // Fields directly mapped to columns
      title: validatedMetadata.title,
      status: validatedMetadata.status,
      tenant_id: this.tenantId,
      created_by: userId, // Map uploaded_by -> created_by
      created_at: new Date().toISOString(),
      // Nullable columns - convert undefined to null
      case_id: validatedMetadata.case_id ?? null,
      client_id: validatedMetadata.client_id ?? null,
      gcs_path: fileName ?? null, // Map file_path -> gcs_path
      // JSON columns
      metadata: recordMetadata as Json, // Assign the prepared JSON object
      // Required columns not in metadata schema
      content: '', // Add required content field, default to empty string initially
      // Other columns like embedding_status, template_id, etc., will use DB defaults or null
    };

    const { data: documentData, error: documentError } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .insert(documentRecord)
      .select()
      .single();

    if (documentError) {
      // If document metadata creation fails, try to delete the uploaded file
      await this.supabase
        .storage
        .from('documents')
        .remove([fileName]);

      console.error('Error creating document metadata:', documentError);
      throw documentError;
    }

    return documentData;
  }

  /**
   * Update document metadata
   */
  async updateMetadata(
    id: string,
    metadata: z.infer<typeof UpdateDocumentMetadataSchema>,
    userId: string
  ) {
    // Validate metadata
    const validatedMetadata = UpdateDocumentMetadataSchema.parse(metadata);

    // Update document record
    const { data, error } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .update({
        ...validatedMetadata,
        updated_by: userId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .select()
      .single();

    if (error) {
      console.error('Error updating document metadata:', error);
      throw error;
    }

    return data;
  }

  /**
   * Delete a document and its metadata
   */
  async delete(id: string) {
    // Get document details first
    const { data: document, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .select('file_path')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return false; // Document not found
      }
      console.error('Error fetching document for deletion:', fetchError);
      throw fetchError;
    }

    // Delete document metadata
    const { error: deleteError } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .delete()
      .eq('id', id)
      .eq('tenant_id', this.tenantId);

    if (deleteError) {
      console.error('Error deleting document metadata:', deleteError);
      throw deleteError;
    }

    // Delete the actual file
    // Cast document to avoid TypeScript errors with potential SelectQueryError
    // We use unknown as an intermediate step for safer casting
    const typedDoc = (document as unknown) as AuthoredDocument;
    if (typedDoc?.gcs_path) {
      const { error: storageError } = await this.supabase
        .storage
        .from('documents')
        .remove([typedDoc.gcs_path]);

      if (storageError) {
        console.error('Error deleting document file:', storageError);
        // We don't throw here because the metadata is already deleted
      }
    }

    return true;
  }

  /**
   * Get download URL for a document
   */
  async getDownloadUrl(id: string) {
    // Get document details first
    const { data: document, error: fetchError } = await this.supabase
      .schema('tenants')
      .from('authored_documents')
      .select('file_path')
      .eq('id', id)
      .eq('tenant_id', this.tenantId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        throw new Error('Document not found');
      }
      console.error('Error fetching document for download:', fetchError);
      throw fetchError;
    }

    // Get signed URL for download
    // Cast document to ensure type safety
    // We use unknown as an intermediate step for safer casting
    const typedDoc = (document as unknown) as AuthoredDocument;
    const { data, error } = await this.supabase
      .storage
      .from('documents')
      .createSignedUrl(typedDoc.gcs_path || '', 60 * 5); // 5 minute expiration

    if (error) {
      console.error('Error generating download URL:', error);
      throw error;
    }

    return data.signedUrl;
  }

  /**
   * Get documents by case ID
   */
  async getByCaseId(caseId: string, options: { page?: number; limit?: number } = {}) {
    return this.getAll({
      ...options,
      case_id: caseId
    });
  }

  /**
   * Get documents by client ID
   */
  async getByClientId(clientId: string, options: { page?: number; limit?: number } = {}) {
    return this.getAll({
      ...options,
      client_id: clientId
    });
  }

  /**
   * List documents with advanced filtering for document center
   * @param options Filtering options
   */
  async list(options: {
    caseId?: string;
    clientId?: string;
    documentType?: string;
    status?: string;
    searchTerm?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}) {
    const {
      page = 1,
      limit = 20,
      caseId,
      clientId,
      documentType,
      status,
      searchTerm,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;

    let query = this.supabase
      .schema('tenants')
      .from('authored_documents')
      .select(`
        *,
        uploaded_by:users!authored_documents_uploaded_by_fkey(id, email, first_name, last_name),
        case:case_id(id, title),
        client:client_id(id, first_name, last_name, business_name),
        processing_jobs:document_processing_jobs(id, status, job_type, created_at, completed_at)
      `, { count: 'exact' })
      .eq('tenant_id', this.tenantId);

    // Apply filters - use any to break the chain of generic typing
    // This prevents excessive type recursion depth
    let typedQuery = query as any;

    if (caseId) typedQuery = typedQuery.eq('case_id', caseId);
    if (clientId) typedQuery = typedQuery.eq('client_id', clientId);
    if (documentType) typedQuery = typedQuery.eq('document_type', documentType);

    // Status filtering is special because it might be a joined field
    if (status) {
      if (status === 'processing') {
        typedQuery = typedQuery.eq('document_processing_jobs.status', 'processing');
      } else {
        typedQuery = typedQuery.eq('status', status);
      }
    }

    // Apply search if provided
    if (searchTerm) {
      typedQuery = typedQuery.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Apply sorting
    typedQuery = typedQuery.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    typedQuery = typedQuery.range(from, to);

    // Restore the query reference
    query = typedQuery;

    const { data, error, count } = await query;

    if (error) {
      console.error('Error listing documents:', error);
      throw error;
    }

    // Process documents to add processing status
    // Cast data to a safer type to handle potential schema mismatches
    const processedData = (data as any[])?.map(doc => {
      // Safely handle processing_jobs which might not be an array due to a join error
      const processingJobs = Array.isArray(doc.processing_jobs) ? doc.processing_jobs : [];
      // Find the latest job if any exist
      const latestJob = processingJobs.length > 0
        ? [...processingJobs].sort((a: any, b: any) => {
            const dateA = a?.created_at ? new Date(a.created_at).getTime() : 0;
            const dateB = b?.created_at ? new Date(b.created_at).getTime() : 0;
            return dateB - dateA;
          })[0]
        : null;

      return {
        ...doc,
        processing_status: latestJob ? latestJob.status : 'none',
        processing_jobs: undefined // Remove the raw jobs data
      };
    });

    return {
      data: processedData || [],
      total: count || 0
    };
  }

  /**
   * Analyze a document using Gemini 2.0 Flash Thinking
   * @param documentId ID of the document to analyze
   * @param analysisType Type of analysis to perform (text, tasks, medical)
   * @param documentType Type of document for specialized analysis
   * @param caseContext Optional context about the case for better analysis
   */
  async analyzeDocumentWithGemini(
    documentId: string,
    analysisType: 'text' | 'tasks' | 'medical' = 'text',
    documentType: 'general' | 'contract' | 'court_filing' | 'medical_record' = 'general',
    caseContext?: string
  ) {
    try {
      // Get the document details
      const document = await this.getById(documentId);

      if (!document) {
        throw new Error('Document not found');
      }

      // Get the document file from storage
      const downloadUrl = await this.getDownloadUrl(documentId);

      // Fetch the file as a blob
      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
      }

      const fileBlob = await response.blob();
      // Get filename and content type from metadata or use defaults
      const metadata = document.metadata as Record<string, unknown> || {};
      const filename = (typeof metadata === 'object' && metadata?.filename) || document.title || 'document.pdf';
      const contentType = (typeof metadata === 'object' && metadata?.contentType) || 'application/pdf';
      const file = new File([fileBlob], filename, { type: contentType });

      // Create form data for the API request
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentType);
      formData.append('analysis_type', analysisType);
      formData.append('document_id', documentId);

      if (document.case_id) {
        formData.append('case_id', document.case_id);
      }

      if (caseContext) {
        formData.append('case_context', caseContext);
      }

      // Get session token
      const { data: { session }, error: sessionError } = await this.supabase.auth.getSession();

      // Prepare headers
      const headers = new Headers();
      if (session?.access_token) {
        headers.set('Authorization', `Bearer ${session.access_token}`);
      } else {
        console.warn('[DocumentService] No access token found for document analysis.');
        if (sessionError) {
           console.error('[DocumentService] Session error:', sessionError.message);
        }
      }

      // Call the document analysis API
      const analysisResponse = await fetch('/api/documents/analyze', {
        method: 'POST',
        headers: headers, // Pass prepared headers (NO Content-Type for FormData)
        body: formData
      });

      if (!analysisResponse.ok) {
        const errorText = await analysisResponse.text();
        throw new Error(`Failed to analyze document: ${analysisResponse.statusText}. Details: ${errorText}`);
      }

      return await analysisResponse.json();
    } catch (error) {
      console.error('Error analyzing document with Gemini:', error);
      throw error;
    }
  }

  /**
   * Extract tasks from a document using Gemini 2.0 Flash Thinking
   * @param documentId ID of the document to analyze
   * @param caseContext Optional context about the case for better task extraction
   */
  async extractTasksFromDocument(documentId: string, caseContext?: string) {
    return this.analyzeDocumentWithGemini(documentId, 'tasks', 'general', caseContext);
  }

  /**
   * Analyze a medical form using Gemini 2.0 Flash Thinking's OCR capabilities
   * @param documentId ID of the medical form to analyze
   */
  async analyzeMedicalForm(documentId: string) {
    return this.analyzeDocumentWithGemini(documentId, 'medical', 'medical_record');
  }

  /**
   * Get previous document analyses for a document
   * @param documentId ID of the document
   */
  async getDocumentAnalyses(documentId: string) {
    // Direct table query with any type to bypass schema validation
    // This approach avoids assuming any specific RPC function exists
    const { data: analyses, error: analysesError } = await (this.supabase as any)
      .from('authored_document_analyses')
      .select('*')
      .eq('document_id', documentId)
      .eq('tenant_id', this.tenantId)
      .order('created_at', { ascending: false });

    if (analysesError) {
      console.error('Error fetching document analyses:', analysesError);
      throw analysesError;
    }

    return analyses || [];
  }
}
