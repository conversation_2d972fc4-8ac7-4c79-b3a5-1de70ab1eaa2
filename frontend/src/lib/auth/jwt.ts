/**
 * JWT Utilities and Verification
 * Handles JWT token parsing, verification, and debugging
 */

import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import { TenantClaims, AuthR<PERSON>ult, UserRole } from './types';

// Initialize Supabase client for JWT verification
const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

const JWT_SECRET = process.env.SUPABASE_JWT_SECRET || 'super-secret-jwt-token-with-at-least-32-characters';

/**
 * Verifies a JWT token and returns the claims
 * Supports both Supabase tokens and custom JWT tokens
 *
 * @param token The JWT token to verify
 * @returns The verified claims or null if invalid
 */
export async function verifyJwt(token: string): Promise<TenantClaims | null> {
  if (!token) return null;

  try {
    // For Supabase tokens (contain dots), verify with Supabase
    if (token.includes('.')) {
      const { data: { user }, error } = await supabase.auth.getUser(token);

      if (error || !user) {
        console.error('Supabase token verification failed:', error);
        return null;
      }

      // Parse the JWT to get additional claims
      const decoded = jwt.decode(token) as any;
      
      return {
        sub: user.id,
        email: user.email || decoded?.email,
        role: (decoded?.role || user.role || 'client') as UserRole,
        tenant_id: decoded?.tenant_id || user.user_metadata?.tenant_id,
        exp: decoded?.exp,
        iat: decoded?.iat,
        jti: decoded?.jti,
        ...decoded
      };
    }
    // For custom tokens, verify with JWT secret
    else {
      const decoded = jwt.verify(token, JWT_SECRET) as any;

      if (!decoded || !decoded.sub) {
        return null;
      }

      return {
        sub: decoded.sub,
        email: decoded.email,
        role: (decoded.role || 'client') as UserRole,
        tenant_id: decoded.tenant_id,
        exp: decoded.exp,
        iat: decoded.iat,
        jti: decoded.jti,
        ...decoded
      };
    }
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

/**
 * Verifies JWT token from a Next.js request
 * Extracts token from Authorization header or cookies
 *
 * @param req Next.js request object
 * @returns Auth result with user info if successful
 */
export async function verifyJWT(_req: NextRequest): Promise<AuthResult> {
  // Extract token from Authorization header or cookie
  const authHeader = req.headers.get('authorization');
  const token = authHeader ? authHeader.split(' ')[1] : undefined;

  // If no token in header, check cookies
  const cookies = req.cookies;
  const cookieToken = cookies.get('sb-access-token')?.value;

  // Use token from header or cookie
  const accessToken = token || cookieToken;

  if (!accessToken) {
    return { success: false, error: 'No authentication token provided' };
  }

  try {
    const claims = await verifyJwt(accessToken);
    
    if (!claims) {
      return { success: false, error: 'Invalid token' };
    }

    return {
      success: true,
      user: {
        id: claims.sub,
        email: claims.email || '',
        role: claims.role || UserRole.Client,
        tenantId: claims.tenant_id || null,
        exp: claims.exp
      }
    };
  } catch (error) {
    console.error('JWT verification error:', error);
    return { success: false, error: 'Token verification failed' };
  }
}

/**
 * Parses JWT claims without verification (for debugging)
 * WARNING: Do not use for authentication - only for debugging
 *
 * @param token The JWT token to parse
 * @returns The parsed claims or null if invalid
 */
export function parseClaims(token: string): TenantClaims | null {
  if (!token) return null;

  try {
    const decoded = jwt.decode(token) as any;
    
    if (!decoded) return null;

    return {
      sub: decoded.sub,
      email: decoded.email,
      role: decoded.role as UserRole,
      tenant_id: decoded.tenant_id,
      exp: decoded.exp,
      iat: decoded.iat,
      jti: decoded.jti,
      ...decoded
    };
  } catch (error) {
    console.error('JWT parsing failed:', error);
    return null;
  }
}

/**
 * Debug function to inspect JWT claims
 * Provides detailed information about the token structure
 *
 * @param token The JWT token to debug
 * @returns Debug information about the token
 */
export function debugJwtClaims(token: string): {
  valid: boolean;
  claims: TenantClaims | null;
  header: any;
  payload: any;
  errors: string[];
} {
  const errors: string[] = [];
  let claims: TenantClaims | null = null;
  let header: any = null;
  let payload: any = null;

  if (!token) {
    errors.push('No token provided');
    return { valid: false, claims, header, payload, errors };
  }

  try {
    // Decode without verification to get structure
    const decoded = jwt.decode(token, { complete: true }) as any;
    
    if (decoded) {
      header = decoded.header;
      payload = decoded.payload;
      
      claims = {
        sub: payload.sub,
        email: payload.email,
        role: payload.role as UserRole,
        tenant_id: payload.tenant_id,
        exp: payload.exp,
        iat: payload.iat,
        jti: payload.jti,
        ...payload
      };

      // Validate required claims
      if (!payload.sub) errors.push('Missing required claim: sub');
      if (!payload.email) errors.push('Missing required claim: email');
      if (!payload.role) errors.push('Missing required claim: role');
      
      // Check expiration
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        errors.push('Token has expired');
      }
      
      // Validate role
      const validRoles = Object.values(UserRole);
      if (payload.role && !validRoles.includes(payload.role)) {
        errors.push(`Invalid role: ${payload.role}`);
      }
    } else {
      errors.push('Failed to decode token');
    }
  } catch (error) {
    errors.push(`Decode error: ${error}`);
  }

  return {
    valid: errors.length === 0,
    claims,
    header,
    payload,
    errors
  };
}

/**
 * Extracts tenant ID from JWT token
 * Utility function for quick tenant ID extraction
 *
 * @param token The JWT token
 * @returns The tenant ID or null if not found
 */
export function extractTenantId(token: string): string | null {
  const claims = parseClaims(token);
  return claims?.tenant_id || null;
}

/**
 * Extracts user role from JWT token
 * Utility function for quick role extraction
 *
 * @param token The JWT token
 * @returns The user role or null if not found
 */
export function extractRole(token: string): UserRole | null {
  const claims = parseClaims(token);
  return claims?.role || null;
}

/**
 * Checks if a JWT token is expired
 *
 * @param token The JWT token
 * @returns True if the token is expired
 */
export function isTokenExpired(token: string): boolean {
  const claims = parseClaims(token);
  if (!claims?.exp) return true;
  
  return Date.now() >= claims.exp * 1000;
}

/**
 * Gets the remaining time until token expiration
 *
 * @param token The JWT token
 * @returns Remaining time in seconds, or 0 if expired/invalid
 */
export function getTokenTimeRemaining(token: string): number {
  const claims = parseClaims(token);
  if (!claims?.exp) return 0;
  
  const remaining = claims.exp - Math.floor(Date.now() / 1000);
  return Math.max(0, remaining);
}
