/**
 * JWT Utilities for AG-UI Authentication
 * 
 * This file contains utilities for extracting, validating, and handling
 * JWT tokens for AG-UI runtime authentication.
 */
import { NextRequest } from 'next/server';
import { jwtVerify, createRemoteJWKSet } from 'jose';

// Interface for JWT payload with minimal required fields
export interface JWTPayload {
  sub: string;           // Subject (user ID)
  iat: number;           // Issued at
  exp: number;           // Expiration time
  tenant_id?: string;    // Organization/tenant ID
  org_id?: string;       // Alternative organization ID field
  user_metadata?: {      // User metadata (from Supabase)
    organization_id?: string;  // Organization ID in metadata
    role?: string;       // User role
  };
  [key: string]: any;    // Additional custom claims
}

// Custom error for JWT validation failures
export class JWTValidationError extends Error {
  status: number;
  
  constructor(message: string, status = 401) {
    super(message);
    this.name = 'JWTValidationError';
    this.status = status;
  }
}

/**
 * Extract JWT token from the Authorization header
 * @param req Next.js request object
 * @returns JWT token string or undefined if not found
 */
export function extractJwtFromRequest(_req: NextRequest): string | undefined {
  const authHeader = req.headers.get('Authorization');
  
  if (!authHeader) {
    return undefined;
  }
  
  const [type, token] = authHeader.split(' ');
  
  if (type !== 'Bearer') {
    return undefined;
  }
  
  return token;
}

/**
 * Validate JWT token and extract payload
 * @param token JWT token to validate
 * @returns Decoded JWT payload or throws JWTValidationError
 */
export async function validateJwt(token: string): Promise<JWTPayload> {
  try {
    // For Supabase JWT validation, we would use the JWT secret
    const jwtSecret = process.env.SUPABASE_JWT_SECRET;
    
    if (!jwtSecret) {
      throw new JWTValidationError('JWT secret not configured', 500);
    }
    
    // Use the secret to validate the token
    // For more complex setups, you might use a JWKS (JSON Web Key Set)
    const { payload } = await jwtVerify(token, new TextEncoder().encode(jwtSecret));

    // Validate claims like expiration, etc.
    // This is automatically done by jwtVerify in most cases
    
    return payload as JWTPayload;
  } catch (error) {
    if (error instanceof JWTValidationError) {
      throw error;
    }
    
    console.error('JWT validation error:', error);
    throw new JWTValidationError('Invalid token');
  }
}

/**
 * Extract organization ID from JWT payload
 * @param payload JWT payload
 * @returns Organization ID or undefined
 */
export function extractOrganizationId(payload: JWTPayload): string | undefined {
  // Try different possible locations for organization ID
  return payload.tenant_id || 
         payload.org_id || 
         payload.user_metadata?.organization_id;
}

/**
 * Extract user ID from JWT payload
 * @param payload JWT payload
 * @returns User ID (subject) from the token
 */
export function extractUserId(payload: JWTPayload): string {
  return payload.sub;
}

/**
 * Generate authorization header for CopilotKit with context information
 * @param payload JWT payload with organization and user information
 * @returns Authorization header value for CopilotKit
 */
export function generateCopilotAuthHeader(payload: JWTPayload): string {
  // Extract organization and user IDs
  const orgId = extractOrganizationId(payload);
  const userId = extractUserId(payload);
  
  // Create a new JWT specifically for CopilotKit with the context
  // In a real implementation, you might sign this with a different secret
  const contextHeader = `Bearer ${JSON.stringify({
    user_id: userId,
    organization_id: orgId,
    role: payload.user_metadata?.role || 'user',
    // Add any other context needed by CopilotKit
    authenticated: true
  })}`;
  
  return contextHeader;
}
