// frontend/src/lib/actions/intake-actions.ts
import { useCopilotAction } from "@copilotkit/react-core";
import { validateActionInput, createActionSuccess, ensureRequiredFields } from '../utils/ag-actions';
import { cookies } from 'next/headers'; // Import cookies
import { IntakeData, IntakeDataSchema } from "../schemas/intake"; // Changed to relative path
import { z } from 'zod';

/**
 * Interface for intake data collected by the agent
 */
// Removed duplicate definition: interface IntakeData {
//   practice_area: string;
//   case_type: string;
//   case_description: string;
//   previous_attorney_consultation: boolean;
//   intake_priority: 'low' | 'medium' | 'high';
// }

/**
 * Submits intake form data to the backend API.
 * This is a server action.
 *
 * @param formData The validated intake form data.
 * @returns An object indicating success or failure with an error message.
 */
export async function submitIntakeData(intakeData: IntakeData): Promise<{
  success: boolean;
  message?: string; // Optional success message
  error?: string;   // Optional error message
  client_id?: string;
  intake_id?: string;
}> {
  console.log('Server Action: submitIntakeData received:', intakeData);

  try {
    // Validate the data again on the server side (optional but good practice)
    const validationResult = IntakeDataSchema.safeParse(intakeData);
    if (!validationResult.success) {
      console.error('Server Action Validation Error:', validationResult.error);
      return {
        success: false,
        error: 'Invalid form data received on server.',
        // message: validationResult.error.errors.map((e) => e.message).join(', '),
      };
    }

    // Server actions use standard fetch. Auth is handled by middleware on API route.
    const response = await fetch('/api/intake/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Auth headers are automatically forwarded by Next.js or handled by middleware
      },
      body: JSON.stringify(validationResult.data), // Use validated data
    });

    const resultText = await response.text(); // Read body once
    console.log('Server Action: API response status:', response.status);
    console.log('Server Action: API response body:', resultText);

    let result;
    try {
      result = JSON.parse(resultText);
    } catch (e) {
      console.error("Failed to parse API response JSON:", e);
      return {
        success: false,
        error: `Failed to parse API response (Status: ${response.status})`,
      };
    }

    if (!response.ok) {
      console.error('Server Action: API Error Response:', result);
      const errorMessage = result.message || result.error || `API error: ${response.status} ${response.statusText}`;
      return {
        success: false,
        error: errorMessage, // Use error field for errors
      };
    }

    console.log('Intake submitted successfully:', result);

    return {
      success: true,
      message: result.message || 'Intake form submitted successfully!', // Use message for success
      client_id: result.client_id,
      intake_id: result.intake_id,
    };
  } catch (error) {
    console.error('Error submitting intake form:', error);
    return {
      success: false,
      error: `Error: ${error instanceof Error ? error.message : 'Unknown error'} `, // Use error field
    };
  }
}

/**
 * CopilotKit action to submit intake data
 */
interface IntakeActionParams {
  intakeData: IntakeData;
}

export function useSubmitIntakeAction() {
  // Using CopilotKit v2 action format with array parameter structure
  useCopilotAction({
    name: 'submitIntakeForm',
    description: 'Submits the client intake form data after collection.',
    parameters: [{
      name: 'intakeData',
      type: 'object',
      description: 'Client intake form data collected from the conversation.',
      properties: {
        practice_area: { type: 'string', description: 'Area of legal practice' },
        case_type: { type: 'string', description: 'Type of legal case' },
        case_description: { type: 'string', description: 'Detailed description of the case' },
        previous_attorney_consultation: { type: 'boolean', description: 'Whether client consulted another attorney previously' },
        priority: { type: 'string', enum: ['low', 'medium', 'high'], description: 'Case priority level' },
        // Optional fields
        client_id: { type: 'string', description: 'Client ID if already known' },
        date_of_incident: { type: 'string', description: 'Date when the incident occurred' },
        client_contact: { type: 'string', description: 'Client contact information' },
        statute_of_limitations: { type: 'string', description: 'Statute of limitations information' },
        conflicting_parties: { type: 'string', description: 'Parties that might have conflicts of interest' },
        work_status: { type: 'string', description: 'Current work status of the client' },
        status: { type: 'string', description: 'Current status of the intake' }
      },
      required: true
    }],
    handler: async (args: any) => {
      try {
        // Extract the intake data from args
        const intakeData = args.intakeData as Record<string, unknown>;
        
        // Ensure required fields are present
        ensureRequiredFields(intakeData, [
          'practice_area',
          'case_type',
          'case_description',
          'previous_attorney_consultation'
        ]);
        
        // Ensure priority is valid and not undefined
        if (!intakeData.priority || !['low', 'medium', 'high'].includes(intakeData.priority)) {
          intakeData.priority = 'medium'; // Default to medium priority if missing or invalid
        }
        
        // Parse and validate with our schema
        const validatedData = validateActionInput(intakeData, IntakeDataSchema) as IntakeData;
        
        // Call server action with validated data
        const result = await submitIntakeData(validatedData);

        if (result.success) {
          return createActionSuccess(
            result.message || `Intake submitted! Client ID: ${result.client_id}, Intake ID: ${result.intake_id}`,
            { 
              clientId: result.client_id, 
              intakeId: result.intake_id 
            }
          );
        } else {
          // Throw error with the message from the server action result
          throw new Error(result.error || 'Failed to submit intake form via Copilot.');
        }
      } catch (error) {
        // Handle any errors in the validation or submission process
        console.error('Error in submitIntakeForm action:', error);
        throw new Error(error instanceof Error ? error.message : 'Unknown error in intake submission');
      }
    }
  });
}
