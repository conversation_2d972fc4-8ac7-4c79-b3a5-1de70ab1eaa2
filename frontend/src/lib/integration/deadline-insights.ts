import { SupabaseClient } from '@supabase/supabase-js';
import { AuthUser } from '../types';
import { logActivityToNeo4j } from '../neo4j/client';
import { LLMService } from '../llm/service';

/**
 * Service for bi-directional integration between deadline management and insights
 */
export class DeadlineInsightsIntegration {
  private supabase: SupabaseClient;
  private llmService: LLMService;
  private user: AuthUser;

  constructor(
    supabase: SupabaseClient,
    user: AuthUser
  ) {
    this.supabase = supabase;
    this.llmService = new LLMService({
      enableCaching: true
    });
    this.user = user;
  }

  /**
   * Generate time-sensitive insights based on upcoming deadlines
   * and create actionable reminders
   */
  async generateTimeBasedInsights(daysAhead: number = 14): Promise<{
    insights: unknown[];
    actions: unknown[];
  }> {
    try {
      // Fetch upcoming deadlines
      const { data: deadlines, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .select(`
          id,
          title,
          description,
          due_date,
          priority,
          status,
          case_id,
          cases:case_id(title),
          created_at,
          created_by,
          tenant_id
        `)
        .eq('tenant_id', this.user.tenantId)
        .gte('due_date', new Date().toISOString())
        .lte('due_date', new Date(Date.now() + daysAhead * 86400000).toISOString())
        .order('due_date', { ascending: true });

      if (error) throw error;

      if (!deadlines || deadlines.length === 0) {
        return { insights: [], actions: [] };
      }

      // Group deadlines by case
      const deadlinesByCase: Record<string, any[]> = {};
      deadlines.forEach(deadline => {
        const caseId = deadline.case_id || 'uncategorized';
        if (!deadlinesByCase[caseId]) {
          deadlinesByCase[caseId] = [];
        }
        deadlinesByCase[caseId].push(deadline);
      });

      // Generate time-based insights for each case
      const allInsights: unknown[] = [];
      const allActions: unknown[] = [];

      for (const [caseId, caseDeadlines] of Object.entries(deadlinesByCase)) {
        // Get case context if available
        let caseContext = '';
        if (caseId !== 'uncategorized') {
          const { data: caseData } = await this.supabase
            .schema('tenants')
            .from('cases')
            .select('title, description, status, metadata')
            .eq('id', caseId)
            .single();

          if (caseData) {
            caseContext = `
              Case Title: ${caseData.title}
              Status: ${caseData.status}
              Description: ${caseData.description || 'N/A'}
            `;
          }
        }

        // Generate insights using LLM for this group of deadlines
        const prompt = `
          Analyze these upcoming deadlines ${caseContext ? 'for the case:' : ''}
          ${caseContext}

          Deadlines:
          ${caseDeadlines.map(d => `- ${d.title}: due ${new Date(d.due_date).toLocaleDateString()} (${d.priority || 'medium'} priority)`).join('\n')}

          Based on this information, provide:
          1. Critical insights about time sensitivity
          2. Suggested preparation steps
          3. Risk assessment for meeting these deadlines
          4. Recommendations for workload management

          Return results as a JSON object with arrays for "insights" and "actions".
          Each insight should have "message" and "priority" (1-10) fields.
          Each action should have "title", "description", and "suggestedDate" fields.
        `;

        const insightsResponse = await this.llmService.generateResponse(
          prompt,
          'You are an AI assistant that helps legal professionals manage time-sensitive deadlines.',
          { responseFormat: 'json' }
        );

        try {
          const insightsData = JSON.parse(insightsResponse);

          // Process insights
          if (insightsData.insights && Array.isArray(insightsData.insights)) {
            // Add case information and format for the insights system
            const formattedInsights = insightsData.insights.map((insight: any) => ({
              id: `tbi_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              message: insight.message,
              priority: insight.priority || 5,
              relatedEntity: caseId !== 'uncategorized' ? {
                type: 'case',
                id: caseId,
                name: caseDeadlines[0]?.cases?.title || 'Unknown Case'
              } : undefined,
              type: 'deadline',
              timestamp: new Date().toISOString(),
              suggestions: [],
              feedbackId: `fb_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
              aiGenerated: true
            }));

            allInsights.push(...formattedInsights);
          }

          // Process actions
          if (insightsData.actions && Array.isArray(insightsData.actions)) {
            // Format actions for the task management system
            const formattedActions = insightsData.actions.map((action: any) => ({
              title: action.title,
              description: action.description,
              due_date: action.suggestedDate || null,
              case_id: caseId !== 'uncategorized' ? caseId : null,
              tenant_id: this.user.tenantId,
              priority: 'medium',
              status: 'pending',
              created_by: this.user.id,
              metadata: {
                source: 'deadline-insights-integration',
                relatedDeadlines: caseDeadlines.map(d => d.id)
              }
            }));

            allActions.push(...formattedActions);
          }
        } catch (parseError) {
          console.error('[DeadlineInsightsIntegration] Error parsing LLM response:', parseError);
        }
      }

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'DEADLINE_INSIGHTS_GENERATION',
        entityType: 'deadline',
        entityId: 'bulk_analysis',
        metadata: {
          deadlineCount: deadlines.length,
          insightCount: allInsights.length,
          actionCount: allActions.length,
          daysAhead
        }
      });

      return {
        insights: allInsights,
        actions: allActions
      };
    } catch (error) {
      console.error('[DeadlineInsightsIntegration] Error generating time-based insights:', error);
      throw error;
    }
  }

  /**
   * Create or update tasks based on insight recommendations
   */
  async createTasksFromInsights(actions: unknown[]): Promise<{
    success: boolean;
    tasksCreated: number;
    tasks: unknown[];
  }> {
    try {
      if (!actions || actions.length === 0) {
        return { success: true, tasksCreated: 0, tasks: [] };
      }

      // Insert tasks in batch
      const { data: tasks, error } = await this.supabase
        .schema('tenants')
        .from('tasks')
        .insert(actions)
        .select();

      if (error) throw error;

      // Log this integration action
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'TASKS_CREATED_FROM_INSIGHTS',
        entityType: 'task',
        entityId: 'bulk_creation',
        metadata: {
          count: tasks?.length || 0,
          source: 'deadline-insights-integration'
        }
      });

      return {
        success: true,
        tasksCreated: tasks?.length || 0,
        tasks: tasks || []
      };
    } catch (error) {
      console.error('[DeadlineInsightsIntegration] Error creating tasks from insights:', error);
      throw error;
    }
  }

  /**
   * Add calendar event based on insight
   */
  async addCalendarEvent(
    insightId: string,
    eventData: {
      title: string;
      description: string;
      start_time: string;
      end_time: string;
      case_id?: string;
      location?: string;
      attendees?: string[];
    }
  ): Promise<{
    success: boolean;
    event: any;
  }> {
    try {
      // Create calendar event
      const { data: event, error } = await this.supabase
        .schema('tenants')
        .from('calendar_events')
        .insert({
          ...eventData,
          tenant_id: this.user.tenantId,
          created_by: this.user.id,
          metadata: {
            source: 'insight-integration',
            insightId
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'CALENDAR_EVENT_FROM_INSIGHT',
        entityType: 'insight',
        entityId: insightId,
        metadata: {
          eventId: event.id,
          eventTitle: event.title
        }
      });

      return {
        success: true,
        event
      };
    } catch (error) {
      console.error('[DeadlineInsightsIntegration] Error adding calendar event:', error);
      throw error;
    }
  }

  /**
   * Generate suggestions when deadlines are modified
   */
  async getDeadlineChangeRecommendations(
    deadlineId: string,
    changeType: 'created' | 'updated' | 'extended' | 'completed' | 'missed'
  ): Promise<{
    recommendations: string[];
    relatedDeadlines: unknown[];
  }> {
    try {
      // Get deadline details
      const { data: deadline, error } = await this.supabase
        .schema('tenants')
        .from('deadlines')
        .select(`
          id,
          title,
          description,
          due_date,
          priority,
          status,
          case_id,
          cases:case_id(title)
        `)
        .eq('id', deadlineId)
        .single();

      if (error) throw error;
      if (!deadline) {
        throw new Error(`Deadline not found: ${deadlineId}`);
      }

      // Find related deadlines for this case
      let relatedDeadlines: unknown[] = [];
      if (deadline.case_id) {
        const { data, error: relatedError } = await this.supabase
          .schema('tenants')
          .from('deadlines')
          .select('id, title, due_date, priority, status')
          .eq('case_id', deadline.case_id)
          .neq('id', deadlineId)
          .order('due_date', { ascending: true })
          .limit(5);

        if (!relatedError) {
          relatedDeadlines = data || [];
        }
      }

      // Generate recommendations based on deadline change
      const prompt = `
        A deadline in the legal case "${Array.isArray(deadline.cases) ? (deadline.cases[0] as any).title : (deadline.cases as any)?.title || 'Unknown'}" has been ${changeType}:

        Deadline: ${deadline.title}
        Description: ${deadline.description || 'N/A'}
        Due Date: ${new Date(deadline.due_date).toLocaleDateString()}
        Priority: ${deadline.priority || 'medium'}
        Status: ${deadline.status || 'pending'}

        ${relatedDeadlines.length > 0 ? `Related deadlines in this case:
        ${relatedDeadlines.map(d => `- ${d.title}: due ${new Date(d.due_date).toLocaleDateString()} (${d.status})`).join('\n')}` : ''}

        Based on this ${changeType} deadline, provide 3-5 specific recommendations for the legal team.

        Return the recommendations as a JSON array of strings.
      `;

      const recommendationsResponse = await this.llmService.generateResponse(
        prompt,
        'You are an AI assistant that helps legal professionals manage deadlines effectively.',
        { responseFormat: 'json' }
      );

      let recommendations: string[] = [];
      try {
        recommendations = JSON.parse(recommendationsResponse);
      } catch (parseError) {
        console.error('[DeadlineInsightsIntegration] Error parsing recommendations:', parseError);
        recommendations = [
          `Review workload allocation for the "${deadline.title}" deadline.`,
          `Consider updating stakeholders about the ${changeType} deadline.`
        ];
      }

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'DEADLINE_CHANGE_RECOMMENDATIONS',
        entityType: 'deadline',
        entityId: deadlineId,
        metadata: {
          changeType,
          recommendationsCount: recommendations.length
        }
      });

      return {
        recommendations,
        relatedDeadlines
      };
    } catch (error) {
      console.error('[DeadlineInsightsIntegration] Error getting deadline change recommendations:', error);
      throw error;
    }
  }
}
