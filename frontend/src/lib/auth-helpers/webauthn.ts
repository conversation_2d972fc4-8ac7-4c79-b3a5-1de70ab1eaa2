import {
  // All custom WebAuthn logic removed. Use Supabase official MFA API instead.
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse,
} from '@simplewebauthn/server';
import type {
  RegistrationResponseJSON,
  AuthenticationResponseJSON,
} from '@simplewebauthn/types';
import { supabase } from '../supabase';

const rpName = 'AiLex';
const rpID = process.env.NEXT_PUBLIC_APP_URL?.replace(/^https?:\/\//, '')!;
const origin = process.env.NEXT_PUBLIC_APP_URL!;

// Generate WebAuthn registration options and store challenge
export async function generateWebAuthnOptions(userId: string) {
  const options = await generateRegistrationOptions({
    rpName,
    rpID,
    userID: new TextEncoder().encode(userId),
    userName: userId,
    userDisplayName: userId,
    attestationType: 'direct',
  });
  // Persist challenge in DB
  await supabase
    .from('tenants.users' as any)
    .update({ webauthn_challenge: options.challenge })
    .eq('id', userId);
  return options;
}

// Verify registration response and store public key & sign count
export async function verifyWebAuthnRegistration(
  credential: RegistrationResponseJSON,
) {
  // Fetch expected challenge & user from DB
  const { data } = await supabase
    .from('tenants.users' as any)
    .select('webauthn_challenge')
    .eq('id', credential.response.userHandle)
    .single();
  if (!data) throw new Error('User not found');
  const expectedChallenge = data.webauthn_challenge;
  const verification = await verifyRegistrationResponse({
    response: credential,
    expectedChallenge,
    expectedOrigin: origin,
    expectedRPID: rpID,
  });
  if (!verification.verified) {
    throw new Error('WebAuthn registration failed');
  }
  const regInfo = verification.registrationInfo! as any;
  const publicKeyBytes = regInfo.credentialPublicKey || regInfo.credential?.publicKey;
  const credentialID = regInfo.credentialID || regInfo.credential?.id;
  const signCount = regInfo.counter || regInfo.credential?.counter || 0;
  // Store public key, id & signCount
  await supabase
    .from('tenants.users' as any)
    .update({
      webauthn_public_key: Buffer.from(publicKeyBytes).toString('base64'),
      webauthn_cred_id: credentialID,
      webauthn_sign_count: signCount,
      mfa_enrolled: true,
      mfa_method: 'webauthn',
    })
    .eq('id', credential.response.userHandle);
  return verification;
}

// Generate auth options for login
export async function generateWebAuthnAuthOptions(userId: string) {
  const { data } = await supabase
    .from('tenants.users' as any)
    .select('webauthn_public_key, webauthn_cred_id')
    .eq('id', userId)
    .single();
  if (!data || !data.webauthn_public_key) {
    throw new Error('No WebAuthn credential registered');
  }
  const publicKey = Buffer.from(data.webauthn_public_key, 'base64');
  const options = await generateAuthenticationOptions({
    rpID,
    userVerification: 'required',
    allowCredentials: [
      {
        id: data.webauthn_cred_id,
        type: 'public-key',
      },
    ],
  });
  // Persist auth challenge
  await supabase
    .from('tenants.users' as any)
    .update({ webauthn_challenge: options.challenge })
    .eq('id', userId);
  return options;
}

// Verify authentication response
export async function verifyWebAuthnAuthResponse(
  credential: AuthenticationResponseJSON,
) {
  // Fetch expected challenge & stored public key
  const { data } = await supabase
    .from('tenants.users' as any)
    .select('webauthn_challenge, webauthn_public_key, webauthn_sign_count')
    .eq('id', credential.id)
    .single();
  if (!data) throw new Error('User not found');
  const expectedChallenge = data.webauthn_challenge;
  const publicKey = Buffer.from(data.webauthn_public_key, 'base64');
  const prevCounter = data.webauthn_sign_count;
  const verification = await verifyAuthenticationResponse({
    response: credential,
    expectedChallenge,
    expectedOrigin: origin,
    expectedRPID: rpID,
    credential: {
        id: data.webauthn_cred_id,
      publicKey,
      counter: prevCounter,
    },
  });
  if (!verification.verified) {
    throw new Error('WebAuthn authentication failed');
  }
  // Update sign count
  await supabase
    .from('tenants.users' as any)
    .update({ webauthn_sign_count: verification.authenticationInfo.newCounter })
    .eq('id', credential.id);
  return verification;
}
